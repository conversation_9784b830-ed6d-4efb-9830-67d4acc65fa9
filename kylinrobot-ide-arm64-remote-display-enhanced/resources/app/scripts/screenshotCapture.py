#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
截图捕获模块
用于在控件捕获功能中，当元素类型为ICON时，启用截图获取功能
支持传入延迟参数 --delay，以秒为单位
"""

import os
import sys
import time
import json
import tempfile
import tkinter as tk
from tkinter import ttk, simpledialog, messagebox
from PIL import Image, ImageTk
import argparse
import threading

# 检查是否在显示环境中
def check_display_environment():
    """检查是否有可用的显示环境"""
    import os
    display = os.environ.get('DISPLAY')
    if not display:
        return False
    
    # 尝试连接X服务器
    try:
        import subprocess
        result = subprocess.run(['xset', 'q'], capture_output=True, text=True, timeout=5)
        return result.returncode == 0
    except (subprocess.TimeoutExpired, FileNotFoundError, Exception):
        return False

# 检查显示环境
HAS_DISPLAY = check_display_environment()

# 尝试导入GUI相关模块
HAS_PYAUTOGUI = False
HAS_IMAGEGRAB = False

if HAS_DISPLAY:
    try:
        # 设置pyautogui故障安全模式
        import pyautogui
        pyautogui.FAILSAFE = True
        HAS_PYAUTOGUI = True
        print("[INFO] 使用pyautogui进行截图", file=sys.stderr)
    except Exception as e:
        print(f"[WARNING] pyautogui导入失败: {e}", file=sys.stderr)
        try:
            from PIL import ImageGrab
            HAS_IMAGEGRAB = True
            print("[INFO] 使用PIL.ImageGrab进行截图", file=sys.stderr)
        except ImportError:
            print("[WARNING] PIL.ImageGrab也不可用", file=sys.stderr)
else:
    print("[INFO] 未检测到图形显示环境，将尝试使用系统截图工具", file=sys.stderr)

# 系统截图工具检查
def check_system_screenshot_tools():
    """检查系统可用的截图工具"""
    import subprocess
    tools = ['gnome-screenshot', 'scrot', 'import', 'xwd']
    available_tools = []
    
    for tool in tools:
        try:
            result = subprocess.run(['which', tool], capture_output=True, text=True)
            if result.returncode == 0:
                available_tools.append(tool)
        except:
            continue
    
    return available_tools

SYSTEM_SCREENSHOT_TOOLS = check_system_screenshot_tools()
if SYSTEM_SCREENSHOT_TOOLS:
    print(f"[INFO] 检测到系统截图工具: {', '.join(SYSTEM_SCREENSHOT_TOOLS)}", file=sys.stderr)

# 检查是否有任何可用的截图方法
if not HAS_PYAUTOGUI and not HAS_IMAGEGRAB and not SYSTEM_SCREENSHOT_TOOLS:
    print("[ERROR] 无可用的截图方法！请检查图形环境或安装截图工具", file=sys.stderr)

# 确定资源路径
def get_resource_path(relative_path):
    """获取资源的绝对路径，兼容打包后的资源访问"""
    try:
        # PyInstaller创建临时文件夹，将路径存储在_MEIPASS中
        base_path = sys._MEIPASS
    except Exception:
        base_path = os.path.dirname(os.path.abspath(__file__))
    return os.path.join(base_path, relative_path)

class ScreenshotCapture:
    """截图捕获类，用于实现屏幕冻结、矩形选择和图像保存功能"""

    def __init__(self, debug=False, test_suite_path=None, delaySec=3):
        """
        初始化截图捕获类

        参数:
            debug: 是否启用调试输出
            test_suite_path: 测试集路径，如果提供，截图将保存在此路径下
            delaySec: 延迟截图的秒数，默认为3秒
            preview_display_height: 图片预览区域最大高度
        """
        self.debug = debug
        self.root = None
        self.canvas = None
        self.screenshot = None
        self.rect_id = None
        self.start_x = 0
        self.start_y = 0
        self.current_x = 0
        self.current_y = 0
        self.is_drawing = False
        self.result = None
        self.delaySec = delaySec
        self.preview_display_height = 500
        
        # 界面主题颜色
        self.theme_colors = {
            'primary': '#4a6fd6',      # 主色调（蓝色）
            'secondary': '#e7eaf6',    # 次要色调（浅蓝灰）
            'background': '#f5f5f7',   # 背景色（浅灰）
            'text': '#333333',         # 文本色（深灰）
            'success': '#4CAF50',      # 成功色（绿色）
            'warning': '#FFC107',      # 警告色（黄色）
            'error': '#F44336',        # 错误色（红色）
            'highlight': '#FF5722'     # 高亮色（橙色）
        }

        # 保存路径相关
        self.save_dir = test_suite_path
        self.element_name = None
        
        # 动画效果控制
        self.animation_active = False
        self.animation_frame = 0

        if self.debug:
            print("[DEBUG] 截图捕获模块初始化完成", file=sys.stderr)
            if test_suite_path:
                print(f"[DEBUG] 将使用测试集路径保存截图: {test_suite_path}", file=sys.stderr)

    def log(self, message):
        """输出调试日志"""
        if self.debug:
            print(f"[DEBUG] {message}", file=sys.stderr)
    
    def take_system_screenshot(self):
        """使用系统工具进行截图"""
        import subprocess
        import tempfile
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_path = temp_file.name
        temp_file.close()
        
        try:
            if 'scrot' in SYSTEM_SCREENSHOT_TOOLS:
                # 使用scrot截图
                result = subprocess.run(['scrot', temp_path], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    screenshot = Image.open(temp_path)
                    os.unlink(temp_path)
                    return screenshot
            
            elif 'gnome-screenshot' in SYSTEM_SCREENSHOT_TOOLS:
                # 使用gnome-screenshot截图
                result = subprocess.run(['gnome-screenshot', '-f', temp_path], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    screenshot = Image.open(temp_path)
                    os.unlink(temp_path)
                    return screenshot
            
            elif 'import' in SYSTEM_SCREENSHOT_TOOLS:
                # 使用ImageMagick的import命令
                result = subprocess.run(['import', '-window', 'root', temp_path], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    screenshot = Image.open(temp_path)
                    os.unlink(temp_path)
                    return screenshot
                    
            elif 'xwd' in SYSTEM_SCREENSHOT_TOOLS:
                # 使用xwd截图
                temp_xwd = temp_path.replace('.png', '.xwd')
                result = subprocess.run(['xwd', '-root', '-out', temp_xwd], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    # 转换xwd到png
                    convert_result = subprocess.run(['convert', temp_xwd, temp_path], capture_output=True, text=True, timeout=10)
                    if convert_result.returncode == 0:
                        screenshot = Image.open(temp_path)
                        os.unlink(temp_path)
                        os.unlink(temp_xwd)
                        return screenshot
                    os.unlink(temp_xwd)
            
            raise RuntimeError("所有系统截图工具都无法正常工作")
            
        except Exception as e:
            # 清理临时文件
            if os.path.exists(temp_path):
                os.unlink(temp_path)
            raise RuntimeError(f"系统截图失败: {e}")
    
    def start_headless_capture(self):
        """
        在无头模式下启动截图捕获
        
        返回:
            dict: 包含截图信息的字典
        """
        print("[INFO] 检测到无图形环境，启动命令行模式截图", file=sys.stderr)
        
        if not SYSTEM_SCREENSHOT_TOOLS:
            print("[ERROR] 无可用的截图工具！", file=sys.stderr)
            print("[INFO] 请安装以下工具之一：", file=sys.stderr)
            print("  - scrot: sudo apt-get install scrot", file=sys.stderr)
            print("  - gnome-screenshot: sudo apt-get install gnome-screenshot", file=sys.stderr)
            print("  - imagemagick: sudo apt-get install imagemagick", file=sys.stderr)
            return None
        
        try:
            import subprocess
            
            # 获取当前时间戳作为文件名
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
            
            # 确定保存路径
            if self.save_dir:
                if not os.path.exists(self.save_dir):
                    os.makedirs(self.save_dir, exist_ok=True)
                filepath = os.path.join(self.save_dir, filename)
            else:
                filepath = filename
            
            print(f"[INFO] 开始截图，将保存到: {filepath}", file=sys.stderr)
            
            # 延迟截图
            if self.delaySec > 0:
                print(f"[INFO] {self.delaySec}秒后开始截图...", file=sys.stderr)
                for i in range(self.delaySec, 0, -1):
                    print(f"[INFO] 倒计时: {i} 秒", file=sys.stderr)
                    time.sleep(1)
            
            # 执行截图
            success = False
            if 'scrot' in SYSTEM_SCREENSHOT_TOOLS:
                result = subprocess.run(['scrot', filepath], capture_output=True, text=True, timeout=10)
                success = result.returncode == 0
                if not success:
                    print(f"[ERROR] scrot截图失败: {result.stderr}", file=sys.stderr)
            
            elif 'gnome-screenshot' in SYSTEM_SCREENSHOT_TOOLS:
                result = subprocess.run(['gnome-screenshot', '-f', filepath], capture_output=True, text=True, timeout=10)
                success = result.returncode == 0
                if not success:
                    print(f"[ERROR] gnome-screenshot截图失败: {result.stderr}", file=sys.stderr)
            
            elif 'import' in SYSTEM_SCREENSHOT_TOOLS:
                result = subprocess.run(['import', '-window', 'root', filepath], capture_output=True, text=True, timeout=10)
                success = result.returncode == 0
                if not success:
                    print(f"[ERROR] ImageMagick import截图失败: {result.stderr}", file=sys.stderr)
            
            if success and os.path.exists(filepath):
                # 获取图片信息
                try:
                    with Image.open(filepath) as img:
                        width, height = img.size
                    
                    file_size = os.path.getsize(filepath)
                    
                    print(f"[SUCCESS] 截图成功！", file=sys.stderr)
                    print(f"[INFO] 文件路径: {os.path.abspath(filepath)}", file=sys.stderr)
                    print(f"[INFO] 图片尺寸: {width}x{height}", file=sys.stderr)
                    print(f"[INFO] 文件大小: {file_size} 字节", file=sys.stderr)
                    
                    return {
                        'success': True,
                        'file_path': os.path.abspath(filepath),
                        'element_name': f"screenshot_{timestamp}",
                        'coordinates': {'x1': 0, 'y1': 0, 'x2': width, 'y2': height},
                        'size': {'width': width, 'height': height},
                        'file_size': file_size
                    }
                except Exception as e:
                    print(f"[ERROR] 读取截图文件失败: {e}", file=sys.stderr)
                    return None
            else:
                print("[ERROR] 截图失败或文件未生成", file=sys.stderr)
                return None
                
        except Exception as e:
            print(f"[ERROR] 无头模式截图失败: {e}", file=sys.stderr)
            return None
            
    def setup_theme(self, root):
        """设置应用程序主题"""
        # 应用现代风格主题
        style = ttk.Style(root)
        style.theme_use('clam')  # 使用clam主题作为基础
        
        # 自定义样式
        style.configure('TFrame', background=self.theme_colors['background'])
        style.configure('TLabel', background=self.theme_colors['background'], 
                      font=('Microsoft YaHei UI', 10), foreground=self.theme_colors['text'])
        style.configure('TButton', font=('Microsoft YaHei UI', 10), borderwidth=1)
        
        # 按钮样式
        style.configure('Accent.TButton', background=self.theme_colors['primary'], foreground='white')
        style.map('Accent.TButton',
                 background=[('active', '#3c5cb8'), ('pressed', '#2e4a9a')],
                 foreground=[('active', 'white'), ('pressed', 'white')])
                 
        # 警告按钮样式
        style.configure('Warning.TButton', background=self.theme_colors['warning'], foreground='white')
        style.map('Warning.TButton',
                 background=[('active', '#e0a800'), ('pressed', '#d39e00')],
                 foreground=[('active', 'white'), ('pressed', 'white')])
                 
        return style

    def start_capture(self):
        """
        启动截图捕获模式

        返回:
            dict: 包含截图信息的字典，如果用户取消则返回None
        """
        self.log("开始截图捕获")

        # 检查是否可以使用GUI模式
        if not HAS_DISPLAY:
            return self.start_headless_capture()

        # 捕获整个屏幕
        if HAS_PYAUTOGUI:
            self.screenshot = pyautogui.screenshot()
        elif HAS_IMAGEGRAB:
            self.screenshot = ImageGrab.grab()
        elif SYSTEM_SCREENSHOT_TOOLS:
            self.screenshot = self.take_system_screenshot()
        else:
            raise RuntimeError("无可用的截图方法！请检查图形环境或安装截图工具（如scrot、gnome-screenshot等）")
        
        screen_width, screen_height = self.screenshot.size

        self.log(f"屏幕尺寸: {screen_width}x{screen_height}")

        # 创建全屏窗口
        self.root = tk.Tk()
        self.root.title("截图选择")
        self.root.attributes('-fullscreen', True)
        self.root.attributes('-topmost', True)
        
        # 设置应用图标
        try:
            icon_path = get_resource_path("resources/screenshot_icon.png")
            if os.path.exists(icon_path):
                icon = ImageTk.PhotoImage(file=icon_path)
                self.root.iconphoto(True, icon)
        except Exception as e:
            self.log(f"无法加载图标: {e}")
            
        # 设置主题
        self.setup_theme(self.root)

        # 创建画布
        self.canvas = tk.Canvas(self.root, width=screen_width, height=screen_height, 
                              cursor="crosshair", highlightthickness=0, bd=0)
        self.canvas.pack(fill=tk.BOTH, expand=True)

        # 将截图显示在画布上
        self.photo = ImageTk.PhotoImage(self.screenshot)
        self.canvas.create_image(0, 0, image=self.photo, anchor=tk.NW)

        # 创建半透明的顶部控制栏
        control_height = 60
        control_bg = self.theme_colors['primary']
        control_bar = self.canvas.create_rectangle(
            0, 0, screen_width, control_height,
            fill=control_bg, outline='', stipple='gray50', width=0
        )
        
        # 添加半透明背景
        self.canvas.create_rectangle(
            0, 0, screen_width, control_height,
            fill=control_bg, outline='', width=0, stipple='gray50'
        )
        
        # 添加说明文本
        self.instruction_text = self.canvas.create_text(
            screen_width // 2,
            control_height // 2,
            text="请拖动鼠标选择截图区域，按ESC取消",
            fill="white",
            font=("Microsoft YaHei UI", 14, "bold")
        )
        
        # 添加动态选择指示器
        self.info_text = self.canvas.create_text(
            screen_width // 2,
            control_height + 30,
            text="",
            fill=self.theme_colors['highlight'],
            font=("Microsoft YaHei UI", 12),
            state='hidden'
        )
        
        # 添加关闭按钮
        close_button_size = 30
        button_margin = 15
        self.close_button = self.canvas.create_oval(
            screen_width - close_button_size - button_margin, 
            button_margin, 
            screen_width - button_margin, 
            close_button_size + button_margin,
            fill=self.theme_colors['error'],
            outline='white',
            width=2
        )
        # 添加X图标
        self.close_x = self.canvas.create_text(
            screen_width - close_button_size//2 - button_margin,
            close_button_size//2 + button_margin,
            text="×",
            fill="white",
            font=("Arial", 16, "bold")
        )
        # 绑定关闭按钮事件
        self.canvas.tag_bind(self.close_button, "<Button-1>", self.on_cancel)
        self.canvas.tag_bind(self.close_x, "<Button-1>", self.on_cancel)

        # 绑定事件
        self.canvas.bind("<ButtonPress-1>", self.on_mouse_down)
        self.canvas.bind("<B1-Motion>", self.on_mouse_move)
        self.canvas.bind("<ButtonRelease-1>", self.on_mouse_up)
        self.root.bind("<Escape>", self.on_cancel)
        
        # 开始动画效果
        self.start_selection_animation()

        # 显示窗口
        self.root.mainloop()

        # 返回结果
        return self.result
        
    def start_selection_animation(self):
        """
        启动选择区域的动画效果
        """
        if not self.is_drawing and self.root and self.root.winfo_exists():
            pulse_colors = [
                self.theme_colors['primary'],
                '#5a7ce6',  # 亮一点的蓝色
                '#6a8cf6',  # 更亮的蓝色
                '#5a7ce6',  # 回到亮一点的蓝色
                self.theme_colors['primary']
            ]
            
            # 交替显示提示信息
            self.animation_frame = (self.animation_frame + 1) % len(pulse_colors)
            
            # 更新指导文本颜色
            self.canvas.itemconfig(
                self.instruction_text, 
                fill=pulse_colors[self.animation_frame]
            )
            
            # 每100ms更新一次
            if self.root and self.root.winfo_exists():
                self.root.after(100, self.start_selection_animation)

    def on_mouse_down(self, event):
        """处理鼠标按下事件"""
        # 如果点击了控制区域，则不创建选择框
        if event.y < 60:  # 控制栏高度
            return
            
        self.log(f"鼠标按下: ({event.x}, {event.y})")
        self.start_x = event.x
        self.start_y = event.y
        self.is_drawing = True
        
        # 暂停动画
        self.animation_active = False
        
        # 创建选区效果层
        # 1. 半透明遮罩
        self.overlay_id = self.canvas.create_rectangle(
            0, 0, self.canvas.winfo_width(), self.canvas.winfo_height(),
            fill='black', stipple='gray25', width=0, state='normal', tags='overlay'
        )
        
        # 2. 空白选区（将被裁剪出来）
        self.selection_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline='', fill='', width=0, tags='selection'
        )

        # 3. 选区边框
        self.rect_id = self.canvas.create_rectangle(
            self.start_x, self.start_y, self.start_x, self.start_y,
            outline=self.theme_colors['highlight'], width=2, dash=(4, 2), tags='border'
        )
        
        # 4. 边角控制点
        self.control_points = []
        
        # 5. 显示尺寸信息
        self.size_text_id = self.canvas.create_text(
            self.start_x, self.start_y - 10,
            text="0 x 0",
            fill=self.theme_colors['highlight'],
            font=("Microsoft YaHei UI", 10, "bold"),
            tags='size_text'
        )

    def on_mouse_move(self, event):
        """处理鼠标移动事件"""
        if not self.is_drawing:
            return

        self.current_x = event.x
        self.current_y = event.y

        # 计算左上角和右下角坐标
        left = min(self.start_x, self.current_x)
        top = min(self.start_y, self.current_y)
        right = max(self.start_x, self.current_x)
        bottom = max(self.start_y, self.current_y)
        
        # 计算宽度和高度
        width = right - left
        height = bottom - top
        
        # 更新选区大小文本
        size_text = f"{width} x {height}"
        self.canvas.itemconfig(self.size_text_id, text=size_text)
        
        # 移动尺寸文本到选区上方
        text_y = top - 15 if top > 30 else bottom + 15
        text_x = left + width // 2
        self.canvas.coords(self.size_text_id, text_x, text_y)

        # 更新矩形边框
        self.canvas.coords(self.rect_id, left, top, right, bottom)
        
        # 更新选区（透明区域）
        self.canvas.coords(self.selection_id, left, top, right, bottom)
        
        # 更新遮罩中的透明区域
        self.canvas.tag_raise('border')
        self.canvas.tag_raise('size_text')
        
        # 设置光标为十字形，表示可以移动
        self.canvas.config(cursor='crosshair')
        
        # 更新控制点（如果存在）
        self.update_control_points(left, top, right, bottom)
        
    def update_control_points(self, left, top, right, bottom):
        """更新选区四角的控制点"""
        # 先删除现有的控制点
        for point_id in self.control_points:
            self.canvas.delete(point_id)
        self.control_points = []
        
        # 如果选区太小，不显示控制点
        if right - left < 20 or bottom - top < 20:
            return
            
        # 创建四个角的控制点
        point_radius = 4
        point_color = self.theme_colors['primary']
        
        # 左上角
        self.control_points.append(self.canvas.create_oval(
            left - point_radius, top - point_radius,
            left + point_radius, top + point_radius,
            fill=point_color, outline='white', width=1, tags='control_point'
        ))
        
        # 右上角
        self.control_points.append(self.canvas.create_oval(
            right - point_radius, top - point_radius,
            right + point_radius, top + point_radius,
            fill=point_color, outline='white', width=1, tags='control_point'
        ))
        
        # 右下角
        self.control_points.append(self.canvas.create_oval(
            right - point_radius, bottom - point_radius,
            right + point_radius, bottom + point_radius,
            fill=point_color, outline='white', width=1, tags='control_point'
        ))
        
        # 左下角
        self.control_points.append(self.canvas.create_oval(
            left - point_radius, bottom - point_radius,
            left + point_radius, bottom + point_radius,
            fill=point_color, outline='white', width=1, tags='control_point'
        ))

    def on_mouse_up(self, event):
        """处理鼠标释放事件"""
        if not self.is_drawing:
            return

        self.is_drawing = False
        self.current_x = event.x
        self.current_y = event.y

        self.log(f"选择区域: ({self.start_x}, {self.start_y}) - ({self.current_x}, {self.current_y})")

        # 确保坐标正确（左上角和右下角）
        left = min(self.start_x, self.current_x)
        top = min(self.start_y, self.current_y)
        right = max(self.start_x, self.current_x)
        bottom = max(self.start_y, self.current_y)

        # 检查选择区域是否有效
        if right - left < 20 or bottom - top < 20:
            self.show_modern_warning("选择的区域太小", "请重新选择一个更大的区域")
            
            # 删除所有选区元素
            self.canvas.delete(self.rect_id)
            self.canvas.delete(self.selection_id)
            self.canvas.delete(self.overlay_id)
            self.canvas.delete(self.size_text_id)
            for point_id in self.control_points:
                self.canvas.delete(point_id)
                
            # 重新启动动画
            self.is_drawing = False
            self.animation_active = True
            self.start_selection_animation()
            return
            
        # 显示确认消息
        self.canvas.itemconfig(self.info_text, state='normal', 
                               text=f"已选择区域: {right-left} x {bottom-top} 像素。即将开始倒计时...")
        self.canvas.coords(self.info_text, (left + right) // 2, bottom + 30)
        self.canvas.tag_raise(self.info_text)
        
        # 更新UI显示并等待片刻
        self.root.update()
        time.sleep(0.5)

        # 关闭窗口
        self.root.destroy()

        # 延迟截图并倒计时
        self.start_delay_capture((left, top, right, bottom))
        
    def show_modern_warning(self, title, message):
        """显示现代风格的警告信息"""
        # 在画布上直接显示警告信息，避免弹出对话框
        warning_bg = self.canvas.create_rectangle(
            self.canvas.winfo_width()//2 - 150, self.canvas.winfo_height()//2 - 50,
            self.canvas.winfo_width()//2 + 150, self.canvas.winfo_height()//2 + 50,
            fill=self.theme_colors['warning'], outline='white', width=2
        )
        
        # 标题
        warning_title = self.canvas.create_text(
            self.canvas.winfo_width()//2, self.canvas.winfo_height()//2 - 25,
            text=title, fill='white', font=("Microsoft YaHei UI", 14, "bold")
        )
        
        # 消息
        warning_msg = self.canvas.create_text(
            self.canvas.winfo_width()//2, self.canvas.winfo_height()//2 + 5,
            text=message, fill='white', font=("Microsoft YaHei UI", 10)
        )
        
        # 更新显示
        self.root.update()
        
        # 1.5秒后自动消失
        self.root.after(1500, lambda: self.canvas.delete(warning_bg))
        self.root.after(1500, lambda: self.canvas.delete(warning_title))
        self.root.after(1500, lambda: self.canvas.delete(warning_msg))

    def on_cancel(self, event=None):
        """处理取消事件"""
        self.log("用户取消截图")
        
        # 显示取消效果
        try:
            screen_width, screen_height = self.canvas.winfo_width(), self.canvas.winfo_height()
            
            # 创建一个淡出效果
            fade_rect = self.canvas.create_rectangle(
                0, 0, screen_width, screen_height,
                fill=self.theme_colors['background'], stipple='gray25', width=0
            )
            
            # 显示取消消息
            cancel_text = self.canvas.create_text(
                screen_width // 2, screen_height // 2,
                text="已取消截图",
                fill=self.theme_colors['primary'],
                font=("Microsoft YaHei UI", 20, "bold")
            )
            
            # 更新显示
            self.root.update()
            time.sleep(0.3)  # 短暂停顯示消息
        except:
            pass  # 如果有任何错误，忽略并继续关闭
            
        self.result = None
        if self.root and self.root.winfo_exists():
            self.root.destroy()

    def start_delay_capture(self, rect):
        """延迟截图并倒计时"""
        left, top, right, bottom = rect
        # 创建现代风格的倒计时窗口
        countdown_root = tk.Tk()
        countdown_root.overrideredirect(True)
        countdown_root.attributes('-topmost', True)
        
        # 设置主题
        style = ttk.Style(countdown_root)
        style.theme_use('clam')
        
        # 固定在屏幕上方居中，使用更小的尺寸
        if HAS_PYAUTOGUI:
            screen_width, screen_height = pyautogui.size()
        else:
            # 使用tkinter获取屏幕尺寸
            root_temp = tk.Tk()
            root_temp.withdraw()
            screen_width = root_temp.winfo_screenwidth()
            screen_height = root_temp.winfo_screenheight()
            root_temp.destroy()
        size = 120  # 减小尺寸从160到120
        x = (screen_width - size) // 2
        y = screen_height // 4
        countdown_root.geometry(f"{size}x{size}+{x}+{y}")
        
        # 设置半透明背景和圆角
        main_frame = ttk.Frame(countdown_root, padding=10)
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 创建圆形背景的画布，尺寸更小
        canvas = tk.Canvas(main_frame, width=size-20, height=size-20, 
                         bg=self.theme_colors['primary'], highlightthickness=0)
        canvas.pack(fill=tk.BOTH, expand=True, padx=0, pady=0)
        
        # 创建圆形，边距更小
        canvas.create_oval(3, 3, size-23, size-23, 
                          fill=self.theme_colors['primary'], 
                          outline='white', width=1.5)
        
        # 创建标题文本，字体更小
        title_text = canvas.create_text(size//2-10, 25, 
                                      text="准备截图", 
                                      fill="white", 
                                      font=("Microsoft YaHei UI", 10, "bold"))
        
        # 倒计时数字，字体更小
        count_text = canvas.create_text(size//2-10, size//2, 
                                      text=str(self.delaySec), 
                                      fill="white", 
                                      font=("Microsoft YaHei UI", 32, "bold"))
        
        # 添加一个进度圈
        progress_arc = None
        
        def update_counter(count, start_angle=90, extent=0):
            # 更新数字
            canvas.itemconfig(count_text, text=str(count))
            
            # 更新进度圈（先删除旧的）
            nonlocal progress_arc
            if progress_arc:
                canvas.delete(progress_arc)
                
            # 新的进度器绘制，边距和线条更小
            arc_extent = 360 * (1 - count / self.delaySec)  # 计算当前应该显示的弧度
            progress_arc = canvas.create_arc(10, 10, size-30, size-30, 
                                            start=90, extent=-arc_extent, 
                                            outline=self.theme_colors['highlight'], 
                                            width=3, style="arc")
            
            if count > 1:
                # 创建动画效果
                steps = 10  # 每秒的动画步数
                for i in range(1, steps + 1):
                    # 计算每一小步的弧度
                    step_extent = 360 * (1 - (count - 1 + (steps - i) / steps) / self.delaySec)
                    countdown_root.after(i * (1000 // steps), 
                                        lambda e=step_extent: update_progress(e))
                    
                countdown_root.after(1000, lambda: update_counter(count-1))
            else:
                # 最后一秒，切换文本
                canvas.itemconfig(count_text, text="")
                canvas.itemconfig(title_text, text="正在截图...")
                
                # 创建一个动画效果
                flash_circle = canvas.create_oval(size//2-25, size//2-25, size//2+5, size//2+5, 
                                               fill="white", outline="white")
                                               
                def expand_circle(r=1, max_r=30):
                    if r <= max_r:
                        canvas.coords(flash_circle, 
                                    size//2-25-r, size//2-25-r, 
                                    size//2+5+r, size//2+5+r)
                        canvas.update()
                        countdown_root.after(10, lambda: expand_circle(r+1, max_r))
                    else:
                        # 隐藏倒计时窗口，确保无遮挡
                        countdown_root.withdraw()
                        countdown_root.update_idletasks()
                        # 等待窗口彻底隐藏
                        time.sleep(0.5)
                        # 捕获最新屏幕并截图所选区域
                        if HAS_PYAUTOGUI:
                            screenshot_img = pyautogui.screenshot()
                        elif HAS_IMAGEGRAB:
                            screenshot_img = ImageGrab.grab()
                        else:
                            screenshot_img = self.screenshot  # 使用原有的截图
                        countdown_root.destroy()
                        self.screenshot = screenshot_img
                        self.show_naming_dialog(rect)
                        
                expand_circle()
                
        def update_progress(extent):
            nonlocal progress_arc
            if progress_arc and countdown_root.winfo_exists():
                canvas.delete(progress_arc)
                progress_arc = canvas.create_arc(10, 10, size-30, size-30, 
                                              start=90, extent=-extent, 
                                              outline=self.theme_colors['highlight'], 
                                              width=3, style="arc")
        
        # 开始倒计时
        countdown_root.after(0, lambda: update_counter(self.delaySec))
        countdown_root.mainloop()

    def show_naming_dialog(self, rect):
        """
        显示现代风格的命名对话框

        参数:
            rect: 选择的矩形区域 (left, top, right, bottom)
        """
        left, top, right, bottom = rect
        width = right - left
        height = bottom - top
        self.log(f"显示命名对话框，选择区域: {rect}")

        # 创建现代风格对话框
        dialog_root = tk.Tk()
        dialog_root.title("截图元素命名")
        dialog_root.attributes('-topmost', True)
        dialog_root.resizable(False, False)
        
        # 设置应用图标
        try:
            icon_path = get_resource_path("resources/screenshot_icon.png")
            if os.path.exists(icon_path):
                icon = ImageTk.PhotoImage(file=icon_path)
                dialog_root.iconphoto(True, icon)
        except Exception as e:
            self.log(f"无法加载图标: {e}")
        
        # 设置主题
        style = self.setup_theme(dialog_root)
        dialog_root.configure(background=self.theme_colors['background'])
        
        # 主框架
        main_frame = ttk.Frame(dialog_root, padding="20 20 20 20", style='TFrame')
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        title_frame = ttk.Frame(main_frame, style='TFrame')
        title_frame.pack(fill=tk.X, pady=(0, 15))
        
        title_label = ttk.Label(title_frame, 
                              text="截图元素命名", 
                              font=('Microsoft YaHei UI', 14, 'bold'), 
                              foreground=self.theme_colors['primary'])
        title_label.pack(side=tk.LEFT)
        
        # 裁剪区域预览
        crop_img = self.screenshot.crop(rect)
        
        # 计算预览尺寸（最大宽度250）
        max_width = 250
        if width > max_width:
            ratio = max_width / width
            display_width = max_width
            display_height = int(height * ratio)
        else:
            display_width = width
            display_height = height
            
        # 调整预览图像
        preview_img = crop_img.resize((display_width, display_height), Image.LANCZOS)
        self.create_image_preview(main_frame, preview_img)
        
        # 尺寸信息
        info_text = f"尺寸: {width} × {height} 像素"
        info_label = ttk.Label(main_frame, text=info_text, 
                             foreground=self.theme_colors['text'],
                             font=("Microsoft YaHei UI", 10))
        info_label.pack(pady=(0, 20))
        
        # 名称输入区域
        input_frame = ttk.Frame(main_frame, style='TFrame')
        input_frame.pack(fill=tk.X, pady=(0, 20))
        
        name_label = ttk.Label(input_frame, text="元素名称:", 
                             font=("Microsoft YaHei UI", 10))
        name_label.grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=5)
        
        name_var = tk.StringVar()
        if self.element_name:  # 如果有默认值
            name_var.set(self.element_name)
        
        name_entry = ttk.Entry(input_frame, textvariable=name_var, width=30, 
                             font=("Microsoft YaHei UI", 10))
        name_entry.grid(row=0, column=1, sticky=tk.W+tk.E, pady=5)
        name_entry.focus()
        
        # 底部按钮区域
        button_frame = ttk.Frame(main_frame, style='TFrame')
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        # 状态标签
        status_var = tk.StringVar(value="")
        status_label = ttk.Label(button_frame, textvariable=status_var, 
                              foreground=self.theme_colors['primary'],
                              font=("Microsoft YaHei UI", 9, "italic"))
        status_label.pack(side=tk.LEFT, padx=5)
        
        def save_action():
            # 获取用户输入的名称
            element_name = name_var.get().strip()
            if not element_name:
                # 显示错误提示
                dialog_root.bell()  # 提示音
                status_var.set("请输入元素名称!")
                status_label.config(foreground=self.theme_colors['error'])
                name_entry.focus()
                return
                
            # 更新状态
            save_button.config(state="disabled")
            cancel_button.config(state="disabled")
            status_var.set("正在保存截图...")
            status_label.config(foreground=self.theme_colors['primary'])
            dialog_root.update()
            
            # 记录元素名称
            self.log(f"用户输入的元素名称: {element_name}")
            self.element_name = element_name
            
            # 保存截图
            saved_path = self.save_screenshot(rect, element_name)
            
            if saved_path:
                self.log(f"截图已保存到: {saved_path}")
                
                # 更新状态显示成功
                status_var.set(f"截图已保存: {os.path.basename(saved_path)}")
                status_label.config(foreground=self.theme_colors['success'])
                dialog_root.update()
                
                # 构建结果
                self.result = {
                    'success': True,
                    'element_name': element_name,
                    'image_path': saved_path,
                    'rect': {
                        'left': rect[0],
                        'top': rect[1],
                        'right': rect[2],
                        'bottom': rect[3],
                        'width': width,
                        'height': height
                    }
                }
                
                # 短暂停显示成功消息
                dialog_root.after(800, dialog_root.destroy)
            else:
                # 保存失败
                self.log("保存截图失败")
                status_var.set("保存截图失败!")
                status_label.config(foreground=self.theme_colors['error'])
                save_button.config(state="normal")
                cancel_button.config(state="normal")
                self.result = None
        
        def cancel_action():
            self.log("用户取消命名")
            self.result = None
            dialog_root.destroy()
        
        # 按钮
        save_button = ttk.Button(button_frame, text="保存", command=save_action, 
                              style='Accent.TButton', width=10)
        save_button.pack(side=tk.RIGHT, padx=5)
        
        cancel_button = ttk.Button(button_frame, text="取消", command=cancel_action, 
                                width=10)
        cancel_button.pack(side=tk.RIGHT, padx=5)
        
        # 事件绑定
        dialog_root.bind('<Return>', lambda e: save_action())
        dialog_root.bind('<Escape>', lambda e: cancel_action())
        
        # 居中显示
        dialog_root.update_idletasks()
        width = dialog_root.winfo_width()
        height = dialog_root.winfo_height()
        x = (dialog_root.winfo_screenwidth() // 2) - (width // 2)
        y = (dialog_root.winfo_screenheight() // 2) - (height // 2)
        dialog_root.geometry(f"{width}x{height}+{x}+{y}")
        
        # 显示对话框
        dialog_root.mainloop()

    def create_image_preview(self, main_frame, preview_img):
        preview_photo = ImageTk.PhotoImage(preview_img)
        _img_width = preview_photo.width()
        _img_height = preview_photo.height()

        # 预览框架
        preview_frame = ttk.Frame(main_frame, padding=5, relief="solid", borderwidth=1)
        preview_frame.pack(pady=(0, 15), padx=10)

        # 创建 Canvas 和垂直滚动条
        canvas = tk.Canvas(preview_frame, highlightthickness=0)
        vsb = ttk.Scrollbar(preview_frame, orient="vertical", command=canvas.yview)
        canvas.configure(yscrollcommand=vsb.set)

        # 放置Canvas
        inner_frame = ttk.Frame(canvas)
        canvas.create_window((0, 0), window=inner_frame, anchor="nw")

        # 设置Canvas高度
        if _img_height > self.preview_display_height:
            canvas.configure(height=self.preview_display_height, width=_img_width)
            vsb.pack(side=tk.RIGHT, fill=tk.Y)
        else:
            canvas.configure(height=_img_height, width=_img_width)
            vsb.pack_forget()

        # 布局Canvas
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        # 配置滚动区域
        def update_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        inner_frame.bind("<Configure>", update_scroll_region)

        # 图像预览
        preview_label = ttk.Label(inner_frame, image=preview_photo, background="white")
        preview_label.image = preview_photo
        preview_label.pack()

    def save_screenshot(self, rect, element_name):
        """
        保存截图

        参数:
            rect: 选择的矩形区域 (left, top, right, bottom)
            element_name: 元素名称

        返回:
            str: 保存的文件路径，如果保存失败则返回None
        """
        try:
            # 裁剪图像
            cropped_image = self.screenshot.crop(rect)

            # 确定保存路径
            img_dir = None

            # 1. 首先检查是否有传入的测试集路径
            if self.save_dir:
                self.log(f"使用传入的测试集路径: {self.save_dir}")
                img_dir = os.path.join(self.save_dir, 'screenshots')
                os.makedirs(img_dir, exist_ok=True)
            else:
                # 2. 如果没有传入路径，尝试获取测试用例目录
                self.log("尝试从KylinRobot获取测试用例目录")
                try:
                    from extensions.KylinRobot_v2.common import common as cm

                    try:
                        # 尝试获取当前测试用例ID
                        case_id = cm.case_id
                        # 构建图像保存目录
                        img_dir = os.path.join(cm.allure_img_dir, str(case_id))
                        # 确保目录存在
                        os.makedirs(img_dir, exist_ok=True)
                    except Exception as e:
                        self.log(f"获取测试用例目录失败: {e}")
                        # 使用临时目录
                        img_dir = None
                except ImportError as e:
                    self.log(f"导入KylinRobot模块失败: {e}")
                    img_dir = None

            # 3. 如果前两种方法都失败，使用临时目录
            if not img_dir:
                self.log("使用临时目录保存截图")
                img_dir = tempfile.gettempdir()

            # 构建文件名
            file_name = f"{element_name}.png"
            file_path = os.path.join(img_dir, file_name)

            self.log(f"截图将保存到: {file_path}")

            # 保存图像
            cropped_image.save(file_path)

            return file_path

        except Exception as e:
            self.log(f"保存截图时出错: {e}")
            return None


def capture_screenshot_area(debug=False, test_suite_path=None, delaySec=3):
    """
    启动截图捕获模式，允许用户选择矩形区域

    参数:
        debug: 是否启用调试输出
        test_suite_path: 测试集路径，如果提供，截图将保存在此路径下
        delaySec: 延迟截图的秒数，默认为3秒

    返回:
        dict: 包含截图信息的字典，如果用户取消则返回None
    """
    capture = ScreenshotCapture(debug=debug, test_suite_path=test_suite_path, delaySec=delaySec)
    result = capture.start_capture()

    # 将结果输出到标准输出，以便主进程获取
    if result:
        print(json.dumps(result))
        return result
    else:
        print(json.dumps({"success": False}))
        return None


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument('--debug', action='store_true')
    parser.add_argument('--test-suite-path', type=str)
    parser.add_argument('--delay', type=int, default=3)
    # 忽略多余的脚本路径参数
    args, _ = parser.parse_known_args()
    capture_screenshot_area(debug=args.debug, test_suite_path=args.test_suite_path, delaySec=args.delay)
