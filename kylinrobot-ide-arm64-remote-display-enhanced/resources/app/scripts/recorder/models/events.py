#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""鼠标/键盘事件数据模型"""
from dataclasses import dataclass
from typing import Dict, List, Optional, Tuple, Any


@dataclass
class MouseEvent:
    """鼠标事件数据结构"""
    timestamp: float
    event_type: str  # 'click', 'right_click', 'double_click', 'move', 'scroll', 'drag_start', 'drag_move', 'drag_end', 'hover'
    x: int
    y: int
    button: Optional[str] = None  # 'left', 'right', 'middle'
    pressed: Optional[bool] = None
    scroll_dx: Optional[int] = None
    scroll_dy: Optional[int] = None
    widget_info: Optional[Dict[str, Any]] = None

    # 拖动相关字段
    drag_start_x: Optional[int] = None
    drag_start_y: Optional[int] = None
    drag_distance: Optional[float] = None
    drag_duration: Optional[float] = None
    drag_path: Optional[List[Tuple[int, int]]] = None  # 拖动路径点

    # 双击相关字段
    click_count: Optional[int] = None  # 点击次数
    time_since_last_click: Optional[float] = None  # 距离上次点击的时间


@dataclass
class KeyboardEvent:
    """键盘事件数据结构"""
    timestamp: float
    event_type: str  # 'press', 'release'
    key: str
    key_code: Optional[int] = None
    modifiers: Optional[List[str]] = None
