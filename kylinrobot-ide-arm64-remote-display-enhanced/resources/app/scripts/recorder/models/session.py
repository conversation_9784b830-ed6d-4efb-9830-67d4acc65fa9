#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""录制会话数据模型"""
from dataclasses import dataclass
from typing import Optional, List, Union
from .events import MouseEvent, KeyboardEvent


@dataclass
class RecordingSession:
    """录制会话数据结构"""
    session_id: str
    start_time: float
    end_time: Optional[float] = None
    test_case_id: Optional[str] = None
    mouse_events: List[MouseEvent] = None
    keyboard_events: List[KeyboardEvent] = None
    record_events: List[Union[MouseEvent, KeyboardEvent]] = None

    def __post_init__(self):
        if self.mouse_events is None:
            self.mouse_events = []
        if self.keyboard_events is None:
            self.keyboard_events = []
        if self.record_events is None:
            self.record_events = []
