#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""鼠标拖动检测器"""
import time
import math
import threading
from typing import Optional, Tuple
from ..models.events import MouseEvent
import sys


class DragDetector:
    """
    鼠标拖动检测器
    负责检测和跟踪鼠标拖动操作
    """

    def __init__(self, debug: bool = False):
        self.debug = debug

        # 拖动检测参数
        self.drag_threshold = 10  # 拖动阈值距离（像素）
        self.path_sample_interval = 50  # 路径采样间隔（毫秒）

        # 拖动状态
        self.is_dragging = False
        self.drag_button = None
        self.drag_start_pos = None
        self.drag_start_time = None
        self.drag_path = []
        self.last_path_sample_time = 0

        # 线程锁
        self.lock = threading.Lock()

    def on_mouse_press(self, x: int, y: int, button: str) -> None:
        """处理鼠标按下事件"""
        with self.lock:
            # 记录可能的拖动起始点
            self.drag_start_pos = (x, y)
            self.drag_start_time = time.time()
            self.drag_button = button
            self.drag_path = [(x, y)]
            self.last_path_sample_time = time.time()

            print(f"[INFO] 记录拖动起始点: ({x}, {y}) 按钮={button}", file=sys.stderr)

    def on_mouse_move(self, x: int, y: int) -> Optional[MouseEvent]:
        """处理鼠标移动事件，检测拖动"""
        with self.lock:
            if not self.drag_start_pos or not self.drag_button:
                return None

            current_time = time.time()
            start_x, start_y = self.drag_start_pos

            # 计算移动距离
            distance = math.sqrt((x - start_x)**2 + (y - start_y)** 2)

            if not self.is_dragging and distance > self.drag_threshold:
                # 开始拖动
                self.is_dragging = True

                print(f"[INFO] 🎯 检测到拖动开始: 起始({start_x}, {start_y}) 当前({x}, {y}) 距离={distance:.1f}px", file=sys.stderr)

                # 创建拖动开始事件
                return MouseEvent(
                    timestamp=self.drag_start_time,
                    event_type='drag_start',
                    x=start_x,
                    y=start_y,
                    button=self.drag_button,
                    pressed=True
                )

            elif self.is_dragging:
                # 拖动进行中

                # 采样路径点
                if current_time - self.last_path_sample_time >= self.path_sample_interval / 1000:
                    self.drag_path.append((x, y))
                    self.last_path_sample_time = current_time

                # 创建拖动移动事件
                return MouseEvent(
                    timestamp=current_time,
                    event_type='drag_move',
                    x=x,
                    y=y,
                    button=self.drag_button,
                    pressed=True,
                    drag_start_x=start_x,
                    drag_start_y=start_y,
                    drag_distance=distance
                )

            return None

    def on_mouse_release(self, x: int, y: int, button: str) -> Optional[MouseEvent]:
        """处理鼠标释放事件，结束拖动"""
        with self.lock:
            # 检查是否有拖动起始点且按钮匹配
            if self.drag_start_pos and button == self.drag_button:
                start_x, start_y = self.drag_start_pos
                drag_duration = time.time() - self.drag_start_time
                final_distance = math.sqrt((x - start_x)**2 + (y - start_y)** 2)

                print(f"[INFO] 鼠标释放检查: 起始({start_x}, {start_y}) 结束({x}, {y}) 距离={final_distance:.1f}px 阈值={self.drag_threshold}px", file=sys.stderr)

                # 检查是否超过拖动阈值
                if final_distance > self.drag_threshold:
                    # 是拖动事件
                    # 添加最终位置到路径
                    self.drag_path.append((x, y))

                    print(f"[INFO] 🏁 拖动结束: 起始({start_x}, {start_y}) 结束({x}, {y}) 距离={final_distance:.1f}px 时长={drag_duration:.2f}s", file=sys.stderr)

                    # 创建拖动结束事件
                    drag_end_event = MouseEvent(
                        timestamp=time.time(),
                        event_type='drag_end',
                        x=x,
                        y=y,
                        button=button,
                        pressed=False,
                        drag_start_x=start_x,
                        drag_start_y=start_y,
                        drag_distance=final_distance,
                        drag_duration=drag_duration,
                        drag_path=self.drag_path.copy()
                    )

                    # 重置拖动状态
                    self._reset_drag_state()

                    return drag_end_event
                else:
                    # 距离不够，不是拖动，重置状态
                    print(f"[INFO] 距离不足，不是拖动: {final_distance:.1f}px < {self.drag_threshold}px", file=sys.stderr)
                    self._reset_drag_state()

            return None

    def _reset_drag_state(self):
        """重置拖动状态"""
        self.is_dragging = False
        self.drag_button = None
        self.drag_start_pos = None
        self.drag_start_time = None
        self.drag_path = []
        self.last_path_sample_time = 0
