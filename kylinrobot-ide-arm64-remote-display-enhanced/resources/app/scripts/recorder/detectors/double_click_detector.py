#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""鼠标双击检测器"""
import math
import threading
from typing import Optional
from ..models.events import MouseEvent
import sys


class DoubleClickDetector:
    """
    双击检测器
    负责检测鼠标双击操作
    """

    def __init__(self, debug: bool = False):
        self.debug = debug

        # 双击检测参数
        self.double_click_threshold = 500  # 双击时间阈值（毫秒）
        self.double_click_distance = 5     # 双击位置阈值（像素）

        # 双击状态
        self.last_click_time = 0
        self.last_click_pos = None
        self.last_click_button = None
        self.click_count = 0

        # 线程锁
        self.lock = threading.Lock()

    def on_mouse_click(self, x: int, y: int, button: str, timestamp: float) -> Optional[MouseEvent]:
        """处理鼠标点击事件，检测双击"""
        with self.lock:
            current_time = timestamp
            time_since_last = (current_time - self.last_click_time) * 1000  # 转换为毫秒

            # 检查是否为连续点击
            is_consecutive = (
                self.last_click_pos and
                button == self.last_click_button and
                time_since_last <= self.double_click_threshold and
                math.hypot(x - self.last_click_pos[0], y - self.last_click_pos[1]) <= self.double_click_distance
            )

            if is_consecutive:
                # 连续点击，增加计数
                self.click_count += 1
                if self.debug:
                    print(f"[DEBUG] 连续点击检测: 次数={self.click_count}, 时间差={time_since_last:.1f}ms", file=sys.stderr)

                # 创建双击事件
                event = MouseEvent(
                    timestamp=current_time,
                    event_type='double_click' if self.click_count >= 2 else 'click',
                    x=x,
                    y=y,
                    button=button,
                    pressed=False,
                    click_count=self.click_count,
                    time_since_last_click=time_since_last / 1000  # 转换为秒
                )

                # 超过双击后重置（避免多击）
                if self.click_count >= 2:
                    self._reset_state()
                return event
            else:
                # 非连续点击，重置计数
                self.click_count = 1
                self.last_click_time = current_time
                self.last_click_pos = (x, y)
                self.last_click_button = button

                if self.debug:
                    print(f"[DEBUG] 新点击记录: 位置=({x}, {y}), 按钮={button}", file=sys.stderr)

                # 创建单击事件
                return MouseEvent(
                    timestamp=current_time,
                    event_type='click',
                    x=x,
                    y=y,
                    button=button,
                    pressed=False,
                    click_count=1
                )

    def _reset_state(self):
        """重置检测器状态"""
        self.last_click_time = 0
        self.last_click_pos = None
        self.last_click_button = None
        self.click_count = 0
