import base64
import os
import requests
from dotenv import load_dotenv

load_dotenv()


def image_detection_api(image_dir):
    file_base64 = base64.b64encode(open(image_dir, 'rb').read()).decode('utf-8')
    try:
        resp = requests.post(
            url=os.getenv('PYTORCH_IMAGE_CHECK_URL'),
            files={'file': file_base64},
            timeout=40
        )
        if resp.status_code == 200:
            return resp.json()['control']
        return False
    except Exception as e:
        print("Error occurred while calling the API:", str(e))
        return False


def get_point_element(point:tuple[int, int], image_dir):
	elements = image_detection_api(image_dir=image_dir)
	if not elements:
		return None
	for element in elements:
		if (element["area"][0] <= point[0] <= element["area"][2]
			and element["area"][1] <= point[1] <= element["area"][3]):
			return element
	print("No element found at the given point.")
	return None
