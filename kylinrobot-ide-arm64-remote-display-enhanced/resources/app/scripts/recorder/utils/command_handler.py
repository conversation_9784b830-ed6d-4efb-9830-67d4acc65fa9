#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""命令处理模块 - 负责监听和处理外部命令"""
import select
import sys
import json
import time
import threading
import traceback
from typing import Callable, Dict, Optional, Any


class CommandHandler:
	"""
	命令处理器 - 负责监听stdin命令并处理
	通过回调函数与主程序交互，保持解耦
	"""

	def __init__(
		self,
		debug: bool = False,
		json_output: bool = False,
		stop_event: threading.Event = None,
		pause_callback: Callable[[], bool] = None,
		resume_callback: Callable[[], bool] = None,
		status_callback: Callable[[], Dict[str, Any]] = None,
		update_bounds_callback: Callable[[Dict[str, int]], None] = None
	):
		"""
		初始化命令处理器

		Args:
			debug: 是否启用调试模式
			json_output: 是否以JSON格式输出
			stop_event: 用于通知主程序停止的事件
			pause_callback: 暂停录制的回调函数
			resume_callback: 恢复录制的回调函数
			status_callback: 获取录制状态的回调函数
			update_bounds_callback: 更新录制窗口边界的回调函数
		"""
		self.debug = debug
		self.json_output = json_output
		self.stop_event = stop_event or threading.Event()

		# 回调函数 - 与主程序交互的接口
		self.pause_callback = pause_callback
		self.resume_callback = resume_callback
		self.status_callback = status_callback
		self.update_bounds_callback = update_bounds_callback

		# 命令监听线程
		self.command_thread = None

	def start(self):
		"""启动命令监听线程"""
		if self.command_thread is None or not self.command_thread.is_alive():
			self.command_thread = threading.Thread(
				target=self._listen_for_commands,
				daemon=True
			)
			self.command_thread.start()
			if self.debug:
				print("[DEBUG] 命令监听线程已启动", file=sys.stderr)

	def stop(self):
		"""停止命令监听线程"""
		if self.debug:
			print("[DEBUG] 正在停止命令监听线程", file=sys.stderr)

	# 线程会通过stop_event自动退出

	def _listen_for_commands(self):
		"""监听stdin命令的线程函数"""
		if self.debug:
			print("[DEBUG] 命令监听线程已启动", file=sys.stderr)

		while not self.stop_event.is_set():
			try:
				# 检查stdin是否可用
				if not sys.stdin.isatty() and hasattr(sys.stdin, 'readable') and sys.stdin.readable():
					# 对于非终端stdin（如subprocess），使用select检查
					try:
						ready, _, _ = select.select([sys.stdin], [], [], 0.1)
						if ready:
							command = sys.stdin.readline().strip()
							if command:
								if self.debug:
									print(f"[DEBUG] stdin接收到数据: '{command}'", file=sys.stderr)
								self._handle_command(command)
							else:
								if self.debug:
									print("[DEBUG] stdin有数据但为空行", file=sys.stderr)
					except (OSError, ValueError) as e:
						# select可能在某些环境下不支持stdin
						if self.debug:
							print(f"[DEBUG] select不支持当前stdin，切换到阻塞模式: {e}", file=sys.stderr)
						# 切换到阻塞读取模式
						self._listen_blocking_mode()
						break
				else:
					# 对于终端stdin，使用阻塞读取
					self._listen_blocking_mode()
					break

			except Exception as e:
				if self.debug:
					print(f"[DEBUG] 命令监听异常: {e}", file=sys.stderr)
				# 在异常情况下稍作等待
				time.sleep(0.1)

		if self.debug:
			print("[DEBUG] 命令监听线程已停止", file=sys.stderr)

	def _listen_blocking_mode(self):
		"""阻塞模式监听命令"""
		if self.debug:
			print("[DEBUG] 切换到阻塞模式监听命令", file=sys.stderr)

		try:
			while not self.stop_event.is_set():
				# 设置stdin为非阻塞模式（如果可能）
				import fcntl
				import os

				# 保存原始标志
				orig_flags = fcntl.fcntl(sys.stdin.fileno(), fcntl.F_GETFL)

				# 设置非阻塞
				fcntl.fcntl(sys.stdin.fileno(), fcntl.F_SETFL, orig_flags | os.O_NONBLOCK)

				try:
					command = sys.stdin.readline().strip()
					if command:
						if self.debug:
							print(f"[DEBUG] 阻塞模式接收到命令: '{command}'", file=sys.stderr)
						self._handle_command(command)
				except IOError:
					# 没有数据可读
					pass
				finally:
					# 恢复原始标志
					fcntl.fcntl(sys.stdin.fileno(), fcntl.F_SETFL, orig_flags)

				# 短暂等待
				time.sleep(0.1)

		except ImportError:
			# fcntl不可用，使用简单的阻塞读取
			if self.debug:
				print("[DEBUG] fcntl不可用，使用简单阻塞读取", file=sys.stderr)
			self._listen_simple_blocking()
		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 阻塞模式监听异常: {e}", file=sys.stderr)

	def _listen_simple_blocking(self):
		"""简单阻塞模式监听"""
		if self.debug:
			print("[DEBUG] 使用简单阻塞模式监听", file=sys.stderr)

		try:
			import threading

			def read_stdin():
				"""在单独线程中读取stdin"""
				try:
					while not self.stop_event.is_set():
						try:
							line = sys.stdin.readline()
							if not line:  # EOF
								break
							command = line.strip()
							if command:
								if self.debug:
									print(f"[DEBUG] 简单模式接收到命令: '{command}'", file=sys.stderr)
								self._handle_command(command)
						except Exception as e:
							if self.debug:
								print(f"[DEBUG] 读取stdin异常: {e}", file=sys.stderr)
							break
				except Exception as e:
					if self.debug:
						print(f"[DEBUG] stdin读取线程异常: {e}", file=sys.stderr)

			# 启动stdin读取线程
			stdin_thread = threading.Thread(target=read_stdin, daemon=True)
			stdin_thread.start()

			# 主循环等待停止事件
			while not self.stop_event.is_set():
				time.sleep(0.1)

		except Exception as e:
			if self.debug:
				print(f"[DEBUG] 简单阻塞模式异常: {e}", file=sys.stderr)

	def _handle_command(self, command: str):
		"""处理接收到的命令"""
		try:
			if self.debug:
				print(f"[DEBUG] 收到命令: {command}", file=sys.stderr)

			# 尝试解析JSON格式的命令
			try:
				command_data = json.loads(command)
				if isinstance(command_data, dict) and 'action' in command_data:
					self._handle_json_command(command_data)
					return
			except (json.JSONDecodeError, ValueError):
				# 不是JSON格式，按原有方式处理
				pass

			if command == 'pause':
				if self.debug:
					print(f"[DEBUG] 处理暂停命令", file=sys.stderr)

				if self.pause_callback and self.pause_callback():
					if self.json_output:
						self._output_json_event('status', {'message': '录制已暂停', 'is_paused': True})
					else:
						print("[INFO] 录制已暂停", file=sys.stderr)
				else:
					if self.debug:
						print(f"[DEBUG] 暂停失败", file=sys.stderr)

			elif command == 'resume':
				if self.debug:
					print(f"[DEBUG] 处理恢复命令", file=sys.stderr)

				if self.resume_callback and self.resume_callback():
					if self.json_output:
						self._output_json_event('status', {'message': '录制已恢复', 'is_paused': False})
					else:
						print("[INFO] 录制已恢复", file=sys.stderr)
				else:
					if self.debug:
						print(f"[DEBUG] 恢复失败", file=sys.stderr)

			elif command == 'stop':
				if self.debug:
					print("[DEBUG] 收到停止命令", file=sys.stderr)
				if self.stop_event:
					self.stop_event.set()

			elif command == 'status':
				if self.status_callback:
					status = self.status_callback()
					if self.json_output:
						self._output_json_event('status', status)
					else:
						print(f"[INFO] 录制状态: {status}", file=sys.stderr)
				else:
					if self.debug:
						print("[DEBUG] 未设置状态回调函数", file=sys.stderr)

			else:
				if self.debug:
					print(f"[DEBUG] 未知命令: {command}", file=sys.stderr)

		except Exception as e:
			print(f"[ERROR] 处理命令失败: {e}", file=sys.stderr)
			if self.debug:
				traceback.print_exc(file=sys.stderr)

	def _handle_json_command(self, command_data: dict):
		"""处理JSON格式的命令"""
		try:
			action = command_data.get('action')

			if action == 'update_recorder_window_bounds':
				bounds = command_data.get('bounds')
				if bounds and isinstance(bounds, dict):
					if self.update_bounds_callback:
						self.update_bounds_callback(bounds)
					else:
						if self.debug:
							print("[DEBUG] 未设置窗口边界更新回调", file=sys.stderr)
				else:
					if self.debug:
						print(f"[DEBUG] 无效的窗口位置数据: {bounds}", file=sys.stderr)
			else:
				if self.debug:
					print(f"[DEBUG] 未知的JSON命令动作: {action}", file=sys.stderr)

		except Exception as e:
			print(f"[ERROR] 处理JSON命令失败: {e}", file=sys.stderr)
			if self.debug:
				traceback.print_exc(file=sys.stderr)

	def _output_json_event(self, event_type: str, data: Dict[str, Any]):
		"""输出JSON格式的事件"""
		try:
			output = {
				'type': event_type,
				'timestamp': time.time(),
				'data': data
			}
			print(json.dumps(output), flush=True)
		except Exception as e:
			print(f"[ERROR] 输出JSON事件失败: {e}", file=sys.stderr)
