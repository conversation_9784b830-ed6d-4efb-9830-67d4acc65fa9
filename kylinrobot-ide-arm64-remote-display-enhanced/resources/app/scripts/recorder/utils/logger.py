#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""日志与打印工具模块"""
import sys
import time
from datetime import datetime
from typing import Any, Dict


# 保存原始 print
original_print = print

# 定义带时间戳的 print
def timestamped_print(*args, **kwargs):
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    original_print(f"[{timestamp}] ", *args, **kwargs)

# 替换全局 print
def enable_timestamped_print():
    import builtins
    builtins.print = timestamped_print


def _log_widget_recognition_timing(
    func_name: str,
    start_time: float,
    end_time: float,
    x: int,
    y: int,
    success: bool = True,
    widget_name: str = "Unknown",
    error_msg: str = ""
):
    """记录控件识别耗时，超过1秒时打印醒目警告"""
    duration = end_time - start_time

    if duration > 1.0:
        print("=" * 80, file=sys.stderr)
        print("🚨 控件识别性能警告 🚨", file=sys.stderr)
        print("=" * 80, file=sys.stderr)
        print(f"⏰ 耗时: {duration:.3f}秒 (超过1秒阈值!)", file=sys.stderr)
        print(f"📍 坐标: ({x}, {y})", file=sys.stderr)
        print(f"🔧 方法: {func_name}", file=sys.stderr)
        if success:
            print(f"🎯 控件: {widget_name}", file=sys.stderr)
            print("✅ 状态: 识别成功", file=sys.stderr)
        else:
            print(f"❌ 状态: 识别失败 - {error_msg}", file=sys.stderr)
        print("💡 建议: 考虑优化控件识别算法或增加缓存", file=sys.stderr)
        print("=" * 80, file=sys.stderr)
    elif duration > 0.5:
        print(f"⚠️  [SLOW] 控件识别较慢: {duration:.3f}秒 | 方法: {func_name} | 坐标: ({x}, {y}) | 控件: {widget_name if success else '识别失败'}", file=sys.stderr)
    elif duration > 0.2:
        print(f"🐌 [INFO] 控件识别耗时: {duration:.3f}秒 | 方法: {func_name} | 坐标: ({x}, {y})", file=sys.stderr)
    else:
        print(f"⚡ [FAST] 控件识别快速: {duration:.3f}秒 | 方法: {func_name} | 坐标: ({x}, {y})", file=sys.stderr)
