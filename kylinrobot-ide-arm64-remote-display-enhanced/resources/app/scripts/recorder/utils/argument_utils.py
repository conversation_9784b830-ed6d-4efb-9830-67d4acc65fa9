"""主函数 - 演示如何使用全自动录制管理器"""
import argparse

parser = argparse.ArgumentParser(description='全自动鼠标键盘事件录制器')
parser.add_argument('--debug', action='store_true', help='启用调试模式')
parser.add_argument('--test-case-id', type=str, help='测试用例ID')
parser.add_argument('--app-name', type=str, help='录制的应用名称')
parser.add_argument('--storage-path', type=str, default='recordings', help='录制数据存储路径')
parser.add_argument('--enable_command_listener', action='store_true', default=True, help='禁用命令监听器')
parser.add_argument('--duration', type=int, default=300, help='录制时长（秒）')
parser.add_argument('--json-output', action='store_true', help='启用JSON事件输出模式')
parser.add_argument('--testcase-path', type=str, help='测试用例路径（用于加载对应的app_menu.py）')

args = parser.parse_args()