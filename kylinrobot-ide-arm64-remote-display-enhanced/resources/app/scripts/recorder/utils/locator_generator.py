#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Locator文件生成器
用于GAT全流程录制系统生成标准格式的locator文件
"""

import os
import json
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
import sys

class LocatorGenerator:
    """
    Locator文件生成器
    负责将录制的控件信息保存为标准格式的locator YAML文件
    """

    def __init__(self, testcase_path: Optional[str] = None, debug: bool = False):
        self.debug = debug
        self.testcase_path = testcase_path
        self.locator_dir = None

        if testcase_path:
            self.setup_locator_directory(testcase_path)

    def setup_locator_directory(self, testcase_path: str):
        """设置locator目录路径"""
        try:
            testcase_path = Path(testcase_path).resolve()
            self.locator_dir = testcase_path.parent / "locator"

            # 确保locator目录存在
            self.locator_dir.mkdir(exist_ok=True)

            if self.debug:
                print(f"[DEBUG] Locator目录设置为: {self.locator_dir}", file=sys.stderr)

        except Exception as e:
            print(f"[ERROR] 设置locator目录失败: {e}", file=sys.stderr)
            self.locator_dir = None

    def generate_uni_locator(self, widget_info: Dict[str, Any], driver: str) -> bool:
        """
        生成UNI类型的locator文件

        Args:
            widget_info: UNI控件信息
            driver: 应用程序驱动名称

        Returns:
            bool: 是否生成成功
        """
        if not self.locator_dir:
            print("[ERROR] Locator目录未设置", file=sys.stderr)
            return False

        try:
            # 构建文件名 - 文件名使用下划线分隔
            filename = f"{driver}_uni.yml"
            filepath = self.locator_dir / filename

            # 生成控件key
            key = self._generate_widget_key(widget_info)

            # 创建datamap结构
            datamap = self._create_datamap_from_widget_info(widget_info)

            # 创建YAML内容
            yaml_content = {
                key: {
                    "datamap": datamap
                }
            }

            # 检查文件是否存在，如果存在则合并内容
            existing_content = {}
            if filepath.exists():
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        existing_content = yaml.safe_load(f) or {}
                except Exception as e:
                    print(f"[WARNING] 读取现有locator文件失败: {e}", file=sys.stderr)

            # 检查key是否已存在
            if key in existing_content:
                if self.debug:
                    print(f"[DEBUG] 控件key '{key}' 已存在于 {filename} 中，跳过", file=sys.stderr)
                return True

            # 合并内容
            existing_content.update(yaml_content)

            # 写入文件 - 使用标准YAML格式
            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(existing_content, f,
                         default_flow_style=False,
                         allow_unicode=True,
                         indent=2,
                         sort_keys=False)

            if self.debug:
                print(f"[DEBUG] ✅ 已生成UNI locator: {key} -> {filename}", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 生成UNI locator失败: {e}", file=sys.stderr)
            return False



    def generate_ocr_locator(self, key: str, coordinates: Dict[str, int], driver: str) -> bool:
        """
        生成OCR类型的locator文件

        Args:
            key: 控件key
            coordinates: 坐标信息 {x, y}
            driver: 应用程序驱动名称

        Returns:
            bool: 是否生成成功
        """
        if not self.locator_dir:
            print("[ERROR] Locator目录未设置", file=sys.stderr)
            return False

        try:
            # 构建文件名
            filename = f"{driver}.yml"
            filepath = self.locator_dir / filename

            # 创建YAML内容
            yaml_content = {
                key: {
                    "type": "ocr",
                    "coords": {
                        "x": coordinates["x"],
                        "y": coordinates["y"]
                    }
                }
            }

            # 检查文件是否存在，如果存在则合并内容
            existing_content = {}
            if filepath.exists():
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        existing_content = yaml.safe_load(f) or {}
                except Exception as e:
                    print(f"[WARNING] 读取现有locator文件失败: {e}", file=sys.stderr)

            # 检查key是否已存在
            if key in existing_content:
                if self.debug:
                    print(f"[DEBUG] 控件key '{key}' 已存在于 {filename} 中，跳过", file=sys.stderr)
                return True

            # 合并内容
            existing_content.update(yaml_content)

            # 写入文件 - 使用标准YAML格式
            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(existing_content, f,
                         default_flow_style=False,
                         allow_unicode=True,
                         indent=2,
                         sort_keys=False)

            if self.debug:
                print(f"[DEBUG] ✅ 已生成OCR locator: {key} -> {filename}", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 生成OCR locator失败: {e}", file=sys.stderr)
            return False

    def generate_position_locator(self, key: str, coordinates: Dict[str, int], driver: str) -> bool:
        """
        生成POSITION类型的locator文件

        Args:
            key: 控件key
            coordinates: 坐标信息 {x, y}
            driver: 应用程序驱动名称

        Returns:
            bool: 是否生成成功
        """
        if not self.locator_dir:
            print("[ERROR] Locator目录未设置", file=sys.stderr)
            return False

        try:
            # 构建文件名
            filename = f"{driver}.yml"
            filepath = self.locator_dir / filename

            # 创建YAML内容
            yaml_content = {
                key: {
                    "type": "position",
                    "coords": {
                        "x": coordinates["x"],
                        "y": coordinates["y"]
                    }
                }
            }

            # 检查文件是否存在，如果存在则合并内容
            existing_content = {}
            if filepath.exists():
                try:
                    with open(filepath, 'r', encoding='utf-8') as f:
                        existing_content = yaml.safe_load(f) or {}
                except Exception as e:
                    print(f"[WARNING] 读取现有locator文件失败: {e}", file=sys.stderr)

            # 检查key是否已存在
            if key in existing_content:
                if self.debug:
                    print(f"[DEBUG] 控件key '{key}' 已存在于 {filename} 中，跳过", file=sys.stderr)
                return True

            # 合并内容
            existing_content.update(yaml_content)

            # 写入文件 - 使用标准YAML格式
            with open(filepath, 'w', encoding='utf-8') as f:
                yaml.dump(existing_content, f,
                         default_flow_style=False,
                         allow_unicode=True,
                         indent=2,
                         sort_keys=False)

            if self.debug:
                print(f"[DEBUG] ✅ 已生成POSITION locator: {key} -> {filename}", file=sys.stderr)

            return True

        except Exception as e:
            print(f"[ERROR] 生成POSITION locator失败: {e}", file=sys.stderr)
            return False

    def _generate_widget_key(self, widget_info: Dict[str, Any]) -> str:
        """
        生成控件key

        Args:
            widget_info: 控件信息

        Returns:
            str: 生成的key
        """
        # 如果控件信息中已有Key，直接使用
        if 'Key' in widget_info and widget_info['Key']:
            return widget_info['Key']

        # 否则根据控件信息生成key
        name = widget_info.get('Name', 'Unknown')
        description = widget_info.get('Description', 'N/A')
        role = widget_info.get('Rolename', 'N/A')

        # 生成简化的key格式
        key = f"N{name}-D{description}-R{role}"

        # 清理key中的特殊字符
        key = key.replace("/", "").replace(" ", "").replace("\n", "").replace("_", "-")

        return key

    def _create_datamap_from_widget_info(self, widget_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        从控件信息创建datamap结构

        Args:
            widget_info: 控件信息

        Returns:
            Dict[str, Any]: datamap结构
        """
        # 提取关键字段创建datamap，保持与UNI返回的字段名一致
        datamap = {}

        # 基本信息 - 保持原始字段名
        if 'Name' in widget_info:
            datamap['Name'] = widget_info['Name']
        if 'Rolename' in widget_info:
            datamap['Rolename'] = widget_info['Rolename']  # 修复：保持原始字段名
        if 'Description' in widget_info:
            datamap['Description'] = widget_info['Description']
        if 'ID' in widget_info:
            datamap['ID'] = widget_info['ID']

        # 进程信息
        if 'ProcessName' in widget_info:
            datamap['ProcessName'] = widget_info['ProcessName']
        if 'ProcessID' in widget_info:
            datamap['ProcessID'] = widget_info['ProcessID']

        # 坐标信息
        if 'Coords' in widget_info:
            datamap['Coords'] = widget_info['Coords']

        # 状态和操作
        if 'States' in widget_info:
            datamap['States'] = widget_info['States']
        if 'Actions' in widget_info:
            datamap['Actions'] = widget_info['Actions']

        # 控件层级信息
        if 'ChildrenCount' in widget_info:
            datamap['ChildrenCount'] = widget_info['ChildrenCount']
        if 'Index_in_parent' in widget_info:
            datamap['Index_in_parent'] = widget_info['Index_in_parent']
        if 'ParentPath' in widget_info:
            datamap['ParentPath'] = widget_info['ParentPath']
        if 'ParentCount' in widget_info:
            datamap['ParentCount'] = widget_info['ParentCount']

        # 文本信息
        if 'Text' in widget_info:
            datamap['Text'] = widget_info['Text']

        # 窗口信息
        if 'WindowName' in widget_info:
            datamap['WindowName'] = widget_info['WindowName']
        if 'WindowRoleName' in widget_info:
            datamap['WindowRoleName'] = widget_info['WindowRoleName']
        if 'WindowChildCount' in widget_info:
            datamap['WindowChildCount'] = widget_info['WindowChildCount']

        # 录制信息 - 转换tuple为list以便YAML序列化
        if 'RecordPosition' in widget_info:
            record_pos = widget_info['RecordPosition']
            if isinstance(record_pos, tuple):
                datamap['RecordPosition'] = list(record_pos)
            else:
                datamap['RecordPosition'] = record_pos

        # 重要的内部字段
        if 'Key' in widget_info:
            datamap['Key'] = widget_info['Key']
        if 'MenuElement' in widget_info:
            datamap['MenuElement'] = widget_info['MenuElement']

        return datamap
