[INFO] 脚本目录: /home/<USER>/kylin-robot-ide/scripts
[INFO] 使用 scripts 目录: /home/<USER>/kylin-robot-ide/scripts
[INFO] 找到 xlib_on_demand.py: /home/<USER>/kylin-robot-ide/scripts/xlib_on_demand.py
[INFO] 找到 UNI.py: /home/<USER>/kylin-robot-ide/scripts/UNI.py
[INFO] 设置 GAT_COMMON_PATH: /home/<USER>/kylin-robot-ide/scripts
[INFO] 切换到工作目录: /home/<USER>/kylin-robot-ide/scripts
[INFO] 执行命令: python3 xlib_on_demand.py
[INFO] Added '/home/<USER>/kylin-robot-ide/scripts' to sys.path from GAT_COMMON_PATH env var.
[INFO] X11环境检测成功，已加载X11相关库
[INFO] UNI模块已导入
[INFO] Xlib库已安装，并已初始化线程支持
[INFO] pynput库已安装
[MAIN] 按需显示版Xlib高亮系统启动
[INIT] 初始化按需显示版Xlib高亮系统
[UNI] 初始化完成，显示服务器: x11
[INIT] UNI实例初始化成功
[INIT] 屏幕尺寸: 1512x829
[WINDOW] 创建高亮窗口
[WINDOW] 高亮窗口创建成功
[MOUSE] 开始跟踪鼠标位置
[EVENT] 开始处理X事件
[PYNPUT] 设置pynput全局键盘监听
[PYNPUT] pynput全局键盘监听已启动
[INIT] 初始化完成
[EVENT] 开始处理事件队列
[DEBUG] 检测到Ctrl键按下事件
[DEBUG] 按下的键: Key.ctrl
[KEYBOARD] Ctrl键按下，self.ctrl_pressed设置为True
[DEBUG] 准备调用grab_mouse()方法
[DEBUG] 进入grab_mouse方法
[DEBUG] 当前鼠标抓取状态: grabbed=False
[DEBUG] 准备抓取鼠标
[DEBUG] 鼠标抓取返回结果: 0
[MOUSE] 鼠标已抓取，self.mouse_grabbed设置为True
[DEBUG] Ctrl键按下事件处理完成
[EVENT_QUEUE] 处理highlight事件: (27, 804)
[HIGHLIGHT] 尝试获取并高亮位置 (27, 804) 的控件
[CAPTURE] 调用UNI.kdk_getElement_Uni(27, 804, True)
[UNI] 开始查找坐标(27, 804)处的控件
[KEYBOARD] Ctrl键释放
[DEBUG] 进入ungrab_mouse方法
[DEBUG] 当前鼠标抓取状态: grabbed=True
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | UKUI Panel], 进程ID: 2913
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (27, 804)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-panel.ukui-panel
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-panel.ukui-panel'
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[1]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 标识符匹配(panel): 得分=95
[DEBUG] 应用程序[2]: 'panel-daemon' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: panel-daemon (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'panel-daemon' 中查找坐标 (27, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'panel-daemon' 中未找到包含坐标 (27, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(ukui-panel): 得分=105
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 105
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 105)
[DEBUG] 🔍 高匹配度应用程序 (得分: 105)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-panel' 中查找坐标 (27, 804) 处的控件...
[DEBUG]     找到包含坐标的元素: UKUI Panel (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小1512x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 5)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 2, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 3, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(0, 783) 大小46x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(46, 798) 大小1x15
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(47, 783) 大小46x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1275, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1265, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(93, 783) 大小1008x46
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[2] 'unnamed' (frame): 坐标(1101, 783) 大小320x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[3] 'unnamed' (frame): 坐标(1421, 783) 大小79x46
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[4] 'unnamed' (frame): 坐标(1500, 783) 大小12x46
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1255, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1245, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[1]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[2]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[3]: UKUI Panel (frame)
[DEBUG]     📋 找到主窗口: UKUI Panel (frame)
[DEBUG]     向上遍历[4]: ukui-panel (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 'ukui-panel' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 105)
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[8]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[9]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[12]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[13]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(panel): 得分=95
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'sogou-qimpanel-watchdog' 中查找坐标 (27, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'sogou-qimpanel-watchdog' 中未找到包含坐标 (27, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[17]: 'ukui-volume-control-applet-qt' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[20]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[26]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[29]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[31]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[35]: 'kylin-printer-applet' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[39]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-menu' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[42]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin system monitor' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'Kylin System Monitor' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[48]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylinrobot-ide' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'kylinrobot-ide' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'ukui-session', [2] 'panel-daemon', [3] 'kglobalaccel', [4] 'ukuismserver'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=105
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (27, 804) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 快速模式返回: (0, 783, 46, 46)
[CAPTURE] UNI返回信息: 找到
[HIGHLIGHT] 找到控件: 位置=(0, 783), 大小=46x46
[HIGHLIGHT] 更新高亮窗口大小: 46x46
[HIGHLIGHT] 直接使用控件坐标: (0, 783)
[HIGHLIGHT] 更新高亮位置: (0, 783)
[DEBUG] 进入show_highlight方法
[DEBUG] 当前高亮显示状态: visible=False
[DEBUG] 准备映射四个边框窗口
[DEBUG] 映射第1个边框窗口
[DEBUG] 映射第2个边框窗口
[DEBUG] 映射第3个边框窗口
[DEBUG] 映射第4个边框窗口
[DEBUG] 准备刷新显示
[HIGHLIGHT] 高亮已显示，self.highlight_visible设置为True
[MOUSE] 鼠标移动到: (26, 804)，尝试获取控件
[EVENT] 鼠标按下: 按钮=1, 位置=(27, 804)
[EVENT] 检测到Ctrl+左键点击
[EVENT] 尝试捕获位置 (27, 804) 的控件
[DEBUG] 进入ungrab_mouse方法
[MOUSE] 鼠标释放命令已发送
[MOUSE] 鼠标抓取状态已重置，self.mouse_grabbed设置为False
[DEBUG] 当前鼠标抓取状态: grabbed=True
[EVENT_QUEUE] 处理highlight事件: (26, 804)
[MOUSE] 鼠标释放命令已发送
[MOUSE] 鼠标抓取状态已重置，self.mouse_grabbed设置为False
[HIGHLIGHT] 尝试获取并高亮位置 (26, 804) 的控件
[CAPTURE] 调用UNI.kdk_getElement_Uni(26, 804, True)
[UNI] 开始查找坐标(26, 804)处的控件
[DEBUG] 已清除过期的桌面缓存
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | UKUI Panel], 进程ID: 2913
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (26, 804)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-panel.ukui-panel
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-panel.ukui-panel'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 50
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[1]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 标识符匹配(panel): 得分=95
[DEBUG] 应用程序[2]: 'panel-daemon' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: panel-daemon (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'panel-daemon' 中查找坐标 (26, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'panel-daemon' 中未找到包含坐标 (26, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(ukui-panel): 得分=105
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 105
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 105)
[DEBUG] 🔍 高匹配度应用程序 (得分: 105)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-panel' 中查找坐标 (26, 804) 处的控件...
[DEBUG]     找到包含坐标的元素: UKUI Panel (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小1512x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 5)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 2, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 3, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(0, 783) 大小46x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(46, 798) 大小1x15
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(47, 783) 大小46x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1275, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1265, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(93, 783) 大小1008x46
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[2] 'unnamed' (frame): 坐标(1101, 783) 大小320x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[3] 'unnamed' (frame): 坐标(1421, 783) 大小79x46
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[4] 'unnamed' (frame): 坐标(1500, 783) 大小12x46
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1255, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1245, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[1]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[2]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[3]: UKUI Panel (frame)
[DEBUG]     📋 找到主窗口: UKUI Panel (frame)
[DEBUG]     向上遍历[4]: ukui-panel (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 'ukui-panel' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 105)
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[8]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[9]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[12]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[13]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(panel): 得分=95
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'sogou-qimpanel-watchdog' 中查找坐标 (26, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'sogou-qimpanel-watchdog' 中未找到包含坐标 (26, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[17]: 'ukui-volume-control-applet-qt' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[20]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[26]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[29]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[31]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[35]: 'kylin-printer-applet' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[39]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-menu' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[42]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin system monitor' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'Kylin System Monitor' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[48]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylinrobot-ide' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'kylinrobot-ide' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'ukui-session', [2] 'panel-daemon', [3] 'kglobalaccel', [4] 'ukuismserver'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=105
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (26, 804) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 快速模式返回: (0, 783, 46, 46)
[CAPTURE] UNI返回信息: 找到
[HIGHLIGHT] 找到控件: 位置=(0, 783), 大小=46x46
[HIGHLIGHT] 更新高亮窗口大小: 46x46
[HIGHLIGHT] 直接使用控件坐标: (0, 783)
[HIGHLIGHT] 更新高亮位置: (0, 783)
[DEBUG] 进入show_highlight方法
[DEBUG] 当前高亮显示状态: visible=True
[DEBUG] 高亮已经显示，跳过显示操作
[EVENT_QUEUE] 处理hide_highlight事件
[DEBUG] 检测到Ctrl键按下事件
[DEBUG] 按下的键: Key.ctrl
[KEYBOARD] Ctrl键按下，self.ctrl_pressed设置为True
[DEBUG] 准备调用grab_mouse()方法
[DEBUG] 进入grab_mouse方法
[DEBUG] 当前鼠标抓取状态: grabbed=False
[DEBUG] 准备抓取鼠标
[HIGHLIGHT] 高亮已隐藏
[EVENT_QUEUE] 处理capture事件: (27, 804)
[DEBUG] 鼠标抓取返回结果: 1
[MOUSE] 鼠标已抓取，self.mouse_grabbed设置为True
[DEBUG] Ctrl键按下事件处理完成
[KEYBOARD] Ctrl键释放
[DEBUG] 进入ungrab_mouse方法
[DEBUG] 当前鼠标抓取状态: grabbed=True
[MOUSE] 鼠标释放命令已发送
[MOUSE] 鼠标抓取状态已重置，self.mouse_grabbed设置为False
[CAPTURE] 捕获控件信息: 位置=(27, 804)
[CAPTURE] 调用UNI.kdk_getElement_Uni(27, 804, False)
[UNI] 开始查找坐标(27, 804)处的控件
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | UKUI Panel], 进程ID: 2913
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (27, 804)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-panel.ukui-panel
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-panel.ukui-panel'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 50
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[1]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 标识符匹配(panel): 得分=95
[DEBUG] 应用程序[2]: 'panel-daemon' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: panel-daemon (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'panel-daemon' 中查找坐标 (27, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'panel-daemon' 中未找到包含坐标 (27, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(ukui-panel): 得分=105
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 105
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 105)
[DEBUG] 🔍 高匹配度应用程序 (得分: 105)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-panel' 中查找坐标 (27, 804) 处的控件...
[DEBUG]     找到包含坐标的元素: UKUI Panel (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小1512x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 5)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 2, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 3, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(0, 783) 大小46x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(27, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(46, 798) 大小1x15
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(47, 783) 大小46x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1275, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1265, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(93, 783) 大小1008x46
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[2] 'unnamed' (frame): 坐标(1101, 783) 大小320x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[3] 'unnamed' (frame): 坐标(1421, 783) 大小79x46
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     检查子控件[4] 'unnamed' (frame): 坐标(1500, 783) 大小12x46
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(27, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1255, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1245, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[1]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[2]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[3]: UKUI Panel (frame)
[DEBUG]     📋 找到主窗口: UKUI Panel (frame)
[DEBUG]     向上遍历[4]: ukui-panel (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 'ukui-panel' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 105)
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[8]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[9]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[12]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[13]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(panel): 得分=95
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'sogou-qimpanel-watchdog' 中查找坐标 (27, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'sogou-qimpanel-watchdog' 中未找到包含坐标 (27, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[17]: 'ukui-volume-control-applet-qt' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[20]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[26]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[29]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[31]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[35]: 'kylin-printer-applet' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[39]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-menu' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[42]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin system monitor' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'Kylin System Monitor' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[48]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylinrobot-ide' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'kylinrobot-ide' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'ukui-session', [2] 'panel-daemon', [3] 'kglobalaccel', [4] 'ukuismserver'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=105
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (27, 804) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 控件ParentPath: [2, 0, 0, 0, 0]
[UNI] ParentPath第一个节点索引: 2
[UNI] 找到应用程序对象: ukui-panel
[UNI] ParentPath第一个节点对象: UKUI Panel (角色: frame)
[UNI] 更新WindowRoleName为ParentPath第一个节点角色: frame
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2913, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'ukui-panel', 'Coords': {'x': 0, 'y': 783, 'width': 46, 'height': 46}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 0, 0, 0], 'ParentCount': 5, 'Key': 'NNA-DNA-P20000', 'RecordPosition': (27, 804), 'WindowRoleName': 'frame', 'WindowChildCount': 1}
[UNI] 控件ParentPath: [2, 0, 0, 0, 0]
[UNI] ParentPath第一个节点索引: 2
[UNI] 找到应用程序对象: ukui-panel
[UNI] ParentPath第一个节点对象: UKUI Panel (角色: frame)
[UNI] 控件实际所属窗口: UKUI Panel
[UNI] 提取到控件信息带窗口: {'Name': 'N/A', 'ID': -1, 'ProcessID': 2913, 'Rolename': 'push button', 'Description': 'N/A', 'Index_in_parent': 'N/A', 'ChildrenCount': 0, 'ProcessName': 'ukui-panel', 'Coords': {'x': 0, 'y': 783, 'width': 46, 'height': 46}, 'Text': 'Not available: ', 'Actions': ['Press', 'SetFocus'], 'States': ['enabled', 'focusable', 'sensitive', 'showing', 'visible'], 'ParentPath': [2, 0, 0, 0, 0], 'ParentCount': 5, 'Key': 'NNA-DNA-P20000', 'RecordPosition': (27, 804), 'WindowRoleName': 'frame', 'WindowChildCount': 1, 'WindowName': 'UKUI Panel'}
[UNI] 控件名称生成为: 按钮_(0,783)
[UNI] 控件信息验证通过
[CAPTURE] UNI返回信息: 找到
[CAPTURE] 捕获到控件: 按钮_(0,783), 类型: push button
[CAPTURE] 控件坐标: x=0, y=783, width=46, height=46
[CAPTURE] 控件信息已捕获并输出
[EVENT_QUEUE] 处理hide_highlight事件
[EVENT_QUEUE] 处理highlight事件: (26, 804)
[SIGNAL] 收到信号: 15
[CLEANUP] 开始清理资源
[DEBUG] 进入ungrab_mouse方法
[DEBUG] 当前鼠标抓取状态: grabbed=False
[HIGHLIGHT] 尝试获取并高亮位置 (26, 804) 的控件
[CAPTURE] 调用UNI.kdk_getElement_Uni(26, 804, True)
[UNI] 开始查找坐标(26, 804)处的控件
[DEBUG] 获取新的桌面对象
[DEBUG] 桌面对象获取完成，应用程序数量: 50
[DEBUG] 已触发桌面刷新，应用数: 50
[UNI] 获取到活动窗口: [frame | UKUI Panel], 进程ID: 2913
[DEBUG] 使用缓存的桌面对象
[DEBUG] 🚨 开始警告窗口专项检测，坐标: (26, 804)
[DEBUG] 查找X11窗口对应的AT-SPI控件，窗口类名: ukui-panel.ukui-panel
[DEBUG] 使用缓存的桌面对象
[DEBUG] 当前桌面应用程序数量: 50
[DEBUG] 目标窗口类名: 'ukui-panel.ukui-panel'
[DEBUG] 应用程序数量未变化，尝试强制刷新AT-SPI
[DEBUG] 强制刷新后应用程序数量: 50
[DEBUG] === 开始遍历所有AT-SPI应用程序 ===
[DEBUG]   智能匹配检查: 应用程序='sni-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[0]: 'sni-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-session' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[1]: 'ukui-session' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-session (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-session' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='panel-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 标识符匹配(panel): 得分=95
[DEBUG] 应用程序[2]: 'panel-daemon' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: panel-daemon (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'panel-daemon' 中查找坐标 (26, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'panel-daemon' 中未找到包含坐标 (26, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='kglobalaccel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[3]: 'kglobalaccel' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukuismserver' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[4]: 'ukuismserver' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukuismserver (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukuismserver' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sni-xembed-proxy' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[5]: 'sni-xembed-proxy' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-panel' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(ukui-panel): 得分=105
[DEBUG] 应用程序[6]: 'ukui-panel' -> 匹配得分: 105
[DEBUG] ✅ 找到匹配应用程序: ukui-panel (匹配得分: 105)
[DEBUG] 🔍 高匹配度应用程序 (得分: 105)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'ukui-panel' 中查找坐标 (26, 804) 处的控件...
[DEBUG]     找到包含坐标的元素: UKUI Panel (角色: frame, 深度: 0, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小1512x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 1, 子控件数: 5)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 2, 子控件数: 1)
[DEBUG]     检查子控件[0] 'unnamed' (frame): 坐标(0, 783) 大小93x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (frame)
[DEBUG]     找到包含坐标的元素: unnamed (角色: frame, 深度: 3, 子控件数: 3)
[DEBUG]     检查子控件[0] 'unnamed' (push button): 坐标(0, 783) 大小46x46
[DEBUG]     ✅ 子控件[0] 'unnamed' 包含目标坐标(26, 804)
[DEBUG]     🔍 开始递归搜索子控件[0] 'unnamed' (push button)
[DEBUG]     找到包含坐标的元素: unnamed (角色: push button, 深度: 4, 子控件数: 0)
[DEBUG]     ✅ 找到交互控件: unnamed (角色: push button)
[DEBUG]     🎯 没有找到更好的子控件，返回交互控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(46, 798) 大小1x15
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[2] 'unnamed' (push button): 坐标(47, 783) 大小46x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1275, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1265, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     检查子控件[1] 'unnamed' (frame): 坐标(93, 783) 大小1008x46
[DEBUG]     ❌ 子控件[1] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[2] 'unnamed' (frame): 坐标(1101, 783) 大小320x46
[DEBUG]     ❌ 子控件[2] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[3] 'unnamed' (frame): 坐标(1421, 783) 大小79x46
[DEBUG]     ❌ 子控件[3] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     检查子控件[4] 'unnamed' (frame): 坐标(1500, 783) 大小12x46
[DEBUG]     ❌ 子控件[4] 'unnamed' 不包含目标坐标(26, 804)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1255, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG]     🎯 按钮控件超高加分: +150 (角色:push button)
[DEBUG]     找到 1 个候选子控件:
[DEBUG]       [0] unnamed (角色: push button, 得分: 1245, 面积: 2116, 分支: 0)
[DEBUG]     🎯 优先选择按钮控件: unnamed (角色: push button)
[DEBUG] 🔍 从 1 个候选控件中选择最佳控件
[DEBUG]   候选控件: unnamed (角色: push button)
[DEBUG]     向上遍历[0]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[1]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[2]: unnamed (frame)
[DEBUG]     📋 找到主窗口: unnamed (frame)
[DEBUG]     向上遍历[3]: UKUI Panel (frame)
[DEBUG]     📋 找到主窗口: UKUI Panel (frame)
[DEBUG]     向上遍历[4]: ukui-panel (application)
[DEBUG]     ⏹️ 到达顶级，停止遍历
[DEBUG]   控件 'unnamed' (push button) 来源窗口类型: main
[DEBUG] ✅ 选择主窗口控件: unnamed (角色: push button)
[DEBUG] ✅ 在应用程序 'ukui-panel' 中找到控件: unnamed
[DEBUG] 🎯 更新最佳匹配控件: unnamed (得分: 105)
[DEBUG]   智能匹配检查: 应用程序='用户手册' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[7]: '用户手册' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-watermark' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[8]: 'ukui-watermark' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-watermark (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-watermark' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-upower' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[9]: 'ukui-upower' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-upower (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-upower' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='vdclient' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[10]: 'vdclient' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='麒麟id' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[11]: '麒麟ID' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-sidebar' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[12]: 'ukui-sidebar' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-sidebar (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-sidebar' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-power-manager-tray' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[13]: 'ukui-power-manager-tray' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: ukui-power-manager-tray (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-power-manager-tray' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='secriskbox' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[14]: 'secRiskBox' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-process-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[15]: 'kylin-process-manager' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='sogou-qimpanel-watchdog' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 反向标识符匹配(panel): 得分=95
[DEBUG] 应用程序[16]: 'sogou-qimpanel-watchdog' -> 匹配得分: 95
[DEBUG] ✅ 找到匹配应用程序: sogou-qimpanel-watchdog (匹配得分: 95)
[DEBUG] 🔍 高匹配度应用程序 (得分: 95)，开始详细控件查找...
[DEBUG] 🔍 开始在应用程序 'sogou-qimpanel-watchdog' 中查找坐标 (26, 804) 处的控件...
[DEBUG] ❌ 在高匹配度应用程序 'sogou-qimpanel-watchdog' 中未找到包含坐标 (26, 804) 的控件
[DEBUG]   智能匹配检查: 应用程序='ukui-volume-control-applet-qt' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=9
[DEBUG] 应用程序[17]: 'ukui-volume-control-applet-qt' -> 匹配得分: 9
[DEBUG] ✅ 找到匹配应用程序: ukui-volume-control-applet-qt (匹配得分: 9)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-volume-control-applet-qt' (得分: 9 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-powermanagement' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[18]: 'ukui-powermanagement' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-powermanagement (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-powermanagement' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='screenmonitorgeneral' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[19]: 'screenMonitorGeneral' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[20]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-settings-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[21]: 'ukui-settings-daemon' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-settings-daemon (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-settings-daemon' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-vpn' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[22]: 'kylin-vpn' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='vino-server' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[23]: 'vino-server' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kscreen_backend_launcher' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[24]: 'kscreen_backend_launcher' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='天气' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[25]: '天气' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-kwin' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[26]: 'ukui-kwin' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-kwin (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-kwin' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[27]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='notifysend' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[28]: 'NotifySend' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='polkit-ukui-authentication-agent-1' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=11
[DEBUG] 应用程序[29]: 'polkit-ukui-authentication-agent-1' -> 匹配得分: 11
[DEBUG] ✅ 找到匹配应用程序: polkit-ukui-authentication-agent-1 (匹配得分: 11)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'polkit-ukui-authentication-agent-1' (得分: 11 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[30]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-bluetooth' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[31]: 'ukui-bluetooth' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-bluetooth (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-bluetooth' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylin-nm' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[32]: 'kylin-nm' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin note' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[33]: 'Kylin Note' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-device-daemon' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[34]: 'kylin-device-daemon' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin-printer-applet' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=1
[DEBUG] 应用程序[35]: 'kylin-printer-applet' -> 匹配得分: 1
[DEBUG] ✅ 找到匹配应用程序: kylin-printer-applet (匹配得分: 1)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'kylin-printer-applet' (得分: 1 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='桌面' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[36]: '桌面' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service-dir-manager' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[37]: 'ukui-search-service-dir-manager' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service-dir-manager (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service-dir-manager' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=14
[DEBUG] 应用程序[38]: 'ukui-search-service' -> 匹配得分: 14
[DEBUG] ✅ 找到匹配应用程序: ukui-search-service (匹配得分: 14)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-service' (得分: 14 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search-app-data-service' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=8
[DEBUG] 应用程序[39]: 'ukui-search-app-data-service' -> 匹配得分: 8
[DEBUG] ✅ 找到匹配应用程序: ukui-search-app-data-service (匹配得分: 8)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search-app-data-service' (得分: 8 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='prlcc' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[40]: 'prlcc' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-menu' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[41]: 'ukui-menu' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-menu (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-menu' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='ukui-search' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[42]: 'ukui-search' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-search (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-search' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='sogouimeservice' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[43]: 'sogouImeService' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ksc-defender' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[44]: 'ksc-defender' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='mate-terminal' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[45]: 'mate-terminal' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='xdg-desktop-portal-gtk' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[46]: 'xdg-desktop-portal-gtk' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='kylin system monitor' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[47]: 'Kylin System Monitor' -> 匹配得分: 0
[DEBUG]   智能匹配检查: 应用程序='ukui-notifications' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ✅ 语义相似度匹配: 得分=22
[DEBUG] 应用程序[48]: 'ukui-notifications' -> 匹配得分: 22
[DEBUG] ✅ 找到匹配应用程序: ukui-notifications (匹配得分: 22)
[DEBUG] ⏭️ 跳过低匹配度应用程序 'ukui-notifications' (得分: 22 < 90)，不进行详细控件查找
[DEBUG]   智能匹配检查: 应用程序='kylinrobot-ide' vs 窗口类名='ukui-panel.ukui-panel'
[DEBUG]   过滤通用标识符: 'ukui'
[DEBUG]   提取的应用标识符: ['ukui-panel', 'panel.ukui', 'panel', 'ukui-panel.ukui-panel']
[DEBUG]   ❌ 所有智能匹配规则都失败，得分=0
[DEBUG] 应用程序[49]: 'kylinrobot-ide' -> 匹配得分: 0
[DEBUG] === 第一轮精确匹配完成 ===
[DEBUG] 所有应用程序: [0] 'sni-daemon', [1] 'ukui-session', [2] 'panel-daemon', [3] 'kglobalaccel', [4] 'ukuismserver'...
[DEBUG] ✅ 找到高精度匹配控件: 得分=105
[DEBUG] X11层级检测成功找到控件: unnamed
[INFO] 使用X11层级检测在坐标 (26, 804) 处找到最顶层控件: unnamed
[UNI] 查找控件结果: [push button | ]
[UNI] 快速模式返回: (0, 783, 46, 46)
[CAPTURE] UNI返回信息: 找到
[HIGHLIGHT] 找到控件: 位置=(0, 783), 大小=46x46
[HIGHLIGHT] 更新高亮窗口大小: 46x46
[HIGHLIGHT] 直接使用控件坐标: (0, 783)
[HIGHLIGHT] 更新高亮位置: (0, 783)
[DEBUG] 进入show_highlight方法
[DEBUG] 当前高亮显示状态: visible=False
[DEBUG] 准备映射四个边框窗口
[DEBUG] 映射第1个边框窗口
[DEBUG] 映射第2个边框窗口
[DEBUG] 映射第3个边框窗口
[DEBUG] 映射第4个边框窗口
[DEBUG] 准备刷新显示
[HIGHLIGHT] 高亮已显示，self.highlight_visible设置为True
[EVENT_QUEUE] 处理hide_highlight事件
[HIGHLIGHT] 高亮已隐藏
[MOUSE] 鼠标释放命令已发送
[MOUSE] 鼠标抓取状态已重置，self.mouse_grabbed设置为False
[CLEANUP] 资源清理完成
