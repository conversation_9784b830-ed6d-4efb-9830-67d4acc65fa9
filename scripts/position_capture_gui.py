#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
坐标采集器 - 专业版
高精度鼠标坐标采集工具，支持 X11 与 Wayland 环境
"""

import tkinter as tk
from tkinter import ttk, messagebox
import json
import time
import threading
import sys
import argparse
import os
import subprocess

def _debug_print(*args, **kwargs):
    """只在调试模式下打印信息到stderr"""
    if os.environ.get('GAT_POSITION_DEBUG'):
        print(*args, file=sys.stderr, **kwargs)

def _original_print(*args, **kwargs):
    """正常的print函数，输出到stdout"""
    print(*args, **kwargs)

# 尝试导入pynput，如果失败则设置标志
PYNPUT_AVAILABLE = True
try:
    from pynput import mouse, keyboard
except ImportError as e:
    PYNPUT_AVAILABLE = False
    # 使用调试输出函数
    _debug_print(f"pynput不可用 ({e})，将使用轮询模式")

def detect_environment():
    """检测运行环境"""
    env_info = {
        'wayland_display': os.environ.get('WAYLAND_DISPLAY'),
        'xdg_session_type': os.environ.get('XDG_SESSION_TYPE'),
        'display': os.environ.get('DISPLAY'),
    }

    if env_info['wayland_display'] or env_info['xdg_session_type'] == 'wayland':
        detected_env = 'wayland'
    elif env_info['display']:
        detected_env = 'x11'
    else:
        detected_env = 'unknown'

    # 调试输出（只在启用调试时）
    _debug_print(f"检测到环境: {detected_env}")
    _debug_print(f"环境变量: {env_info}")

    return detected_env, env_info

def get_mouse_position_xdotool():
    """使用xdotool获取鼠标位置"""
    try:
        if not os.environ.get('DISPLAY'):
            os.environ['DISPLAY'] = ':0'

        output = subprocess.check_output(
            ['xdotool', 'getmouselocation'],
            stderr=subprocess.PIPE,
            timeout=3
        ).decode('utf-8').strip()

        parts = output.split()
        x = int(parts[0].split(':')[1])
        y = int(parts[1].split(':')[1])
        return True, (x, y)
    except Exception:
        return False, None

def get_mouse_position_pynput():
    """使用pynput获取鼠标位置"""
    if not PYNPUT_AVAILABLE:
        return False, None
    try:
        from pynput.mouse import Controller
        mouse_controller = Controller()
        pos = mouse_controller.position
        return True, (int(pos[0]), int(pos[1]))
    except Exception:
        return False, None

class CoordinateCapture:
    def __init__(self, root_window, initial_name=None, initial_type=None):
        self.root = root_window
        self.root.title("坐标采集器 - 专业版 v2.0")
        self.root.attributes('-topmost', True)
        self.root.resizable(True, True)
        self.root.minsize(520, 600)

        # 检测环境
        self.environment, self.env_info = detect_environment()

        # 状态变量
        self.is_closing = False
        self.current_x = 0
        self.current_y = 0
        self.locked_x = 0
        self.locked_y = 0
        self.are_coords_locked = False
        self.is_tracking = False
        self.has_focus = True
        self.tracking_method = None

        # 模式控制
        self.manual_mode_locked = False
        self.user_activated_manual = False

        # 监听器
        self.keyboard_listener = None
        self.mouse_listener = None

        # 创建UI
        self.setup_ui(initial_name, initial_type)
        self.center_window(520, 420)

        # 绑定事件
        self.root.bind('<FocusIn>', self.on_focus_in)
        self.root.bind('<FocusOut>', self.on_focus_out)
        self.manual_x_var.trace('w', self.on_manual_input_change)
        self.manual_y_var.trace('w', self.on_manual_input_change)

        # 启动追踪
        self.start_tracking()
        self.update_mode_ui()

        # 立即获取一次鼠标位置
        self.root.after(100, self.initial_mouse_position_update)

        # 调试信息
        _debug_print(f"坐标采集器初始化完成:")
        _debug_print(f"  - 环境: {self.environment}")
        _debug_print(f"  - pynput可用: {PYNPUT_AVAILABLE}")
        _debug_print(f"  - 窗口焦点: {self.has_focus}")
        _debug_print(f"  - 初始坐标: ({self.current_x}, {self.current_y})")

        # ---- 键盘绑定设置 ----
        try:
            # Tkinter 键盘绑定（主要方案）
            def on_tkinter_ctrl(event):
                _debug_print(f"🎯 Tkinter检测到Ctrl键: {event.keysym}")
                self.toggle_lock()

            # 绑定多种Ctrl键事件
            self.root.bind('<Control_L>', on_tkinter_ctrl)
            self.root.bind('<Control_R>', on_tkinter_ctrl)
            self.root.bind('<KeyPress-Control_L>', on_tkinter_ctrl)
            self.root.bind('<KeyPress-Control_R>', on_tkinter_ctrl)

            # 确保窗口可以接收键盘焦点
            self.root.focus_set()

            _debug_print("Tkinter键盘绑定已设置")
        except Exception as e:
            _debug_print(f"设置Tkinter键盘绑定失败: {e}")
            pass

    def setup_ui(self, initial_name=None, initial_type=None):
        """设置UI界面"""
        # 配置样式主题 - 参考插入公共方法窗口样式
        style = ttk.Style()

        # 设置颜色方案 - 与插入公共方法窗口保持一致
        colors = {
            'background': '#ffffff',
            'foreground': '#333333',
            'border_color': '#d4d4d4',
            'input_background': '#ffffff',
            'input_foreground': '#333333',
            'button_background': '#0e639c',
            'button_foreground': '#ffffff',
            'button_hover_background': '#1177bb',
            'button_primary_background': '#0e639c',
            'button_primary_foreground': '#ffffff',
            'button_primary_hover_background': '#1177bb',
            'focus_border': '#007fd4',
            'text_light': '#888888',
            'locked_background': '#fff3cd',
            'locked_foreground': '#856404',
            'locked_border': '#ffeaa7'
        }

        # 应用主题
        style.theme_use('clam')

        # 配置样式 - 参考插入公共方法窗口
        style.configure('App.TFrame', background=colors['background'])
        style.configure('Card.TFrame', background=colors['background'], relief='solid', borderwidth=1)
        style.configure('App.TLabel',
                       background=colors['background'],
                       foreground=colors['foreground'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 13))
        style.configure('Title.TLabel',
                       background=colors['background'],
                       foreground=colors['foreground'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 16, 'bold'))
        style.configure('Subtitle.TLabel',
                       background=colors['background'],
                       foreground=colors['foreground'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11, 'bold'))
        style.configure('Coord.TLabel',
                       background=colors['background'],
                       foreground=colors['foreground'],
                       font=('Consolas', 'Monaco', 'monospace', 11, 'bold'))
        style.configure('LockedCoord.TLabel',
                       background=colors['locked_background'],
                       foreground=colors['locked_foreground'],
                       font=('Consolas', 'Monaco', 'monospace', 11, 'bold'),
                       relief='solid',
                       borderwidth=1)
        style.configure('Status.TLabel',
                       background=colors['background'],
                       foreground=colors['text_light'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11))

        # 按钮样式 - 参考插入公共方法窗口
        style.configure('Primary.TButton',
                       background=colors['button_primary_background'],
                       foreground=colors['button_primary_foreground'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11),
                       padding=(12, 6),
                       relief='flat')
        style.map('Primary.TButton',
                 background=[('active', colors['button_primary_hover_background'])])

        style.configure('Secondary.TButton',
                       background=colors['button_background'],
                       foreground=colors['button_foreground'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11),
                       padding=(12, 6),
                       relief='flat')
        style.map('Secondary.TButton',
                 background=[('active', colors['button_hover_background'])])

        # 输入框样式
        style.configure('App.TEntry',
                       fieldbackground=colors['input_background'],
                       foreground=colors['input_foreground'],
                       bordercolor=colors['border_color'],
                       font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11),
                       padding=(8, 6))
        style.map('App.TEntry',
                 focuscolor=[('focus', colors['focus_border'])])

        self.root.configure(background=colors['background'])

        # 主容器
        main_frame = ttk.Frame(self.root, style='App.TFrame', padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)

        # 标题区域
        header_frame = ttk.Frame(main_frame, style='App.TFrame')
        header_frame.grid(row=0, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        header_frame.columnconfigure(0, weight=1)

        title_label = ttk.Label(header_frame, text="坐标采集器", style='Title.TLabel')
        title_label.grid(row=0, column=0, sticky=tk.W)

        # 显示环境信息
        env_text = f"专业级鼠标坐标采集工具 - {self.environment.upper()}"
        if not PYNPUT_AVAILABLE:
            env_text += " (轮询模式)"
        subtitle_label = ttk.Label(header_frame, text=env_text, style='Status.TLabel')
        subtitle_label.grid(row=1, column=0, sticky=tk.W, pady=(2, 0))

        # 基本信息卡片
        info_card = ttk.LabelFrame(main_frame, text="  基本信息  ", style='Card.TFrame', padding="12")
        info_card.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 12))
        info_card.columnconfigure(1, weight=1)

        ttk.Label(info_card, text="坐标名称", style='App.TLabel').grid(row=0, column=0, sticky=tk.W, pady=8, padx=(0, 15))
        self.name_var = tk.StringVar()
        if initial_name:
            self.name_var.set(initial_name)
        name_entry = ttk.Entry(info_card, textvariable=self.name_var, font=('Segoe UI', 9), width=25)
        name_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=8)
        name_entry.focus()

        ttk.Label(info_card, text="坐标类型", style='App.TLabel').grid(row=1, column=0, sticky=tk.W, pady=8, padx=(0, 15))
        self.type_var = tk.StringVar(value='blank')
        type_options = ['blank', 'text']
        if initial_type and initial_type in type_options:
            self.type_var.set(initial_type)
        type_combo = ttk.Combobox(info_card, textvariable=self.type_var, values=type_options,
                                 state="readonly", font=('Segoe UI', 9), width=22)
        type_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=8)

        # 添加分隔线
        separator2 = ttk.Separator(main_frame, orient='horizontal')
        separator2.grid(row=2, column=0, sticky=(tk.W, tk.E), pady=(0, 16))

        # 坐标采集区域 - 使用更简洁的布局
        coords_section = ttk.Frame(main_frame, style='App.TFrame')
        coords_section.grid(row=3, column=0, sticky=(tk.W, tk.E), pady=(0, 16))
        coords_section.columnconfigure(1, weight=1)

        # 实时坐标显示
        ttk.Label(coords_section, text="实时坐标:", style='App.TLabel').grid(row=0, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 12))
        self.coords_label_var = tk.StringVar(value="X: ---, Y: ---")
        coords_display = ttk.Label(coords_section, textvariable=self.coords_label_var, style='Coord.TLabel')
        coords_display.grid(row=0, column=1, sticky=tk.W, pady=(0, 8))

        # 锁定坐标显示
        ttk.Label(coords_section, text="锁定坐标:", style='App.TLabel').grid(row=1, column=0, sticky=tk.W, pady=(0, 8), padx=(0, 12))
        self.locked_coords_var = tk.StringVar(value="未锁定")
        self.locked_display = ttk.Label(coords_section, textvariable=self.locked_coords_var, style='Coord.TLabel')
        self.locked_display.grid(row=1, column=1, sticky=tk.W, pady=(0, 8))

        # 添加分隔线
        separator3 = ttk.Separator(main_frame, orient='horizontal')
        separator3.grid(row=4, column=0, sticky=(tk.W, tk.E), pady=(0, 16))

        # 采集方式选择 - 参考插入公共方法窗口的按钮组样式
        mode_section = ttk.Frame(main_frame, style='App.TFrame')
        mode_section.grid(row=5, column=0, sticky=(tk.W, tk.E), pady=(0, 16))

        mode_label = ttk.Label(mode_section, text="采集方式:", style='Subtitle.TLabel')
        mode_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        mode_buttons_frame = ttk.Frame(mode_section, style='App.TFrame')
        mode_buttons_frame.grid(row=1, column=0, sticky=tk.W)

        self.auto_mode_btn = ttk.Button(mode_buttons_frame, text="自动采集", command=self.switch_to_auto_mode,
                                       style='Primary.TButton', width=12)
        self.auto_mode_btn.grid(row=0, column=0, sticky=tk.W, padx=(0, 8))

        self.manual_mode_btn = ttk.Button(mode_buttons_frame, text="手动输入", command=self.switch_to_manual_mode,
                                         style='Secondary.TButton', width=12)
        self.manual_mode_btn.grid(row=0, column=1, sticky=tk.W)

        # 状态指示器
        self.status_var = tk.StringVar(value="自动采集模式")
        self.status_label = ttk.Label(mode_section, textvariable=self.status_var, style='Status.TLabel')
        self.status_label.grid(row=2, column=0, sticky=tk.W, pady=(8, 0))

        # 手动输入区域（初始隐藏）
        self.manual_frame = ttk.Frame(main_frame, style='App.TFrame')
        self.manual_frame.grid(row=6, column=0, sticky=(tk.W, tk.E), pady=(0, 16))
        self.manual_frame.columnconfigure(1, weight=1)

        manual_label = ttk.Label(self.manual_frame, text="手动输入坐标:", style='Subtitle.TLabel')
        manual_label.grid(row=0, column=0, sticky=tk.W, pady=(0, 8))

        coord_input_frame = ttk.Frame(self.manual_frame, style='App.TFrame')
        coord_input_frame.grid(row=1, column=0, sticky=(tk.W, tk.E))

        ttk.Label(coord_input_frame, text="X:", style='App.TLabel').grid(row=0, column=0, sticky=tk.W, padx=(0, 8))
        self.manual_x_var = tk.StringVar()
        self.manual_x_entry = ttk.Entry(coord_input_frame, textvariable=self.manual_x_var, style='App.TEntry', width=10)
        self.manual_x_entry.grid(row=0, column=1, sticky=tk.W, padx=(0, 16))

        ttk.Label(coord_input_frame, text="Y:", style='App.TLabel').grid(row=0, column=2, sticky=tk.W, padx=(0, 8))
        self.manual_y_var = tk.StringVar()
        self.manual_y_entry = ttk.Entry(coord_input_frame, textvariable=self.manual_y_var, style='App.TEntry', width=10)
        self.manual_y_entry.grid(row=0, column=3, sticky=tk.W, padx=(0, 16))

        # 手动操作按钮
        manual_button_frame = ttk.Frame(self.manual_frame, style='App.TFrame')
        manual_button_frame.grid(row=2, column=0, sticky=tk.W, pady=(12, 0))

        self.get_current_btn = ttk.Button(manual_button_frame, text="获取当前位置", command=self.get_current_position,
                                         style='Secondary.TButton', width=15)
        self.get_current_btn.grid(row=0, column=0, sticky=tk.W, padx=(0, 8))

        self.manual_lock_btn = ttk.Button(manual_button_frame, text="锁定此坐标", command=self.manual_lock_coords,
                                         style='Primary.TButton', width=12)
        self.manual_lock_btn.grid(row=0, column=1, sticky=tk.W)

        # 绑定手动输入框事件
        self.manual_x_entry.bind('<FocusIn>', self.on_manual_input_focus)
        self.manual_y_entry.bind('<FocusIn>', self.on_manual_input_focus)
        self.manual_x_entry.bind('<FocusOut>', self.on_manual_input_blur)
        self.manual_y_entry.bind('<FocusOut>', self.on_manual_input_blur)

        # 初始隐藏手动输入区域
        self.manual_frame.grid_remove()

        # 操作提示区域
        tips_section = ttk.Frame(main_frame, style='App.TFrame')
        tips_section.grid(row=7, column=0, sticky=(tk.W, tk.E), pady=(0, 16))

        if PYNPUT_AVAILABLE:
            initial_tip = "移动鼠标到目标位置，按 Ctrl 键锁定坐标（如果Ctrl键不工作，请使用下方的按钮）"
        else:
            initial_tip = "使用'获取当前位置'按钮或手动输入坐标，在轮询模式下Ctrl键不可用"
        self.tips_var = tk.StringVar(value=initial_tip)
        tips_label = ttk.Label(tips_section, textvariable=self.tips_var, style='Status.TLabel', wraplength=480)
        tips_label.grid(row=0, column=0, sticky=(tk.W, tk.E))

        # 锁定按钮区域
        lock_section = ttk.Frame(main_frame, style='App.TFrame')
        lock_section.grid(row=8, column=0, sticky=(tk.W, tk.E), pady=(0, 16))

        lock_btn = ttk.Button(lock_section, text="锁定/解锁坐标 (Ctrl)", command=self.toggle_lock,
                             style='Secondary.TButton', width=25)
        lock_btn.grid(row=0, column=0)

        # 调试按钮（仅在调试模式下显示）
        if os.environ.get('GAT_POSITION_DEBUG'):
            def test_update_coords():
                import random
                x = random.randint(100, 999)
                y = random.randint(100, 999)
                self.coords_label_var.set(f"X: {x}, Y: {y}")
                _debug_print(f"手动测试更新坐标: X: {x}, Y: {y}")
                _debug_print(f"coords_label_var 当前值: {self.coords_label_var.get()}")

            test_btn = ttk.Button(lock_section, text="测试更新坐标", command=test_update_coords,
                                 style='Secondary.TButton', width=15)
            test_btn.grid(row=0, column=1, padx=(8, 0))

        # 添加分隔线
        separator_bottom = ttk.Separator(main_frame, orient='horizontal')
        separator_bottom.grid(row=9, column=0, sticky=(tk.W, tk.E), pady=(0, 16))

        # 底部按钮区域 - 参考插入公共方法窗口的footer样式
        footer_frame = ttk.Frame(main_frame, style='App.TFrame')
        footer_frame.grid(row=10, column=0, sticky=(tk.W, tk.E))
        footer_frame.columnconfigure(0, weight=1)

        # 按钮容器 - 右对齐
        button_container = ttk.Frame(footer_frame, style='App.TFrame')
        button_container.grid(row=0, column=0, sticky=tk.E)

        cancel_button = ttk.Button(button_container, text="取消", command=self.cancel_capture,
                                  style='Secondary.TButton', width=12)
        cancel_button.grid(row=0, column=0, padx=(0, 8))

        confirm_button = ttk.Button(button_container, text="确定采集", command=self.confirm_capture,
                                   style='Primary.TButton', width=12)
        confirm_button.grid(row=0, column=1)

        self.root.protocol("WM_DELETE_WINDOW", self.on_window_close)

    def center_window(self, width, height):
        """居中显示窗口"""
        screen_width = self.root.winfo_screenwidth()
        screen_height = self.root.winfo_screenheight()
        x = (screen_width/2) - (width/2)
        y = (screen_height/2) - (height/2)
        self.root.geometry(f'{width}x{height}+{int(x)}+{int(y)}')

    def switch_to_auto_mode(self):
        """切换到自动模式"""
        self.manual_mode_locked = False
        self.user_activated_manual = False
        self.update_mode_ui()

    def switch_to_manual_mode(self):
        """切换到手动模式"""
        self.manual_mode_locked = True
        self.user_activated_manual = True
        self.update_mode_ui()

    def update_mode_ui(self):
        """更新模式界面"""
        # 保存当前锁定状态，避免在模式切换时丢失
        was_locked = self.are_coords_locked
        locked_coords_text = None
        if was_locked:
            # 如果已锁定，重新构建坐标文本
            locked_coords_text = f"X: {self.locked_x}, Y: {self.locked_y}"
            _debug_print(f"保存锁定状态: {locked_coords_text}")
        if self.manual_mode_locked:
            # 手动模式
            self.status_var.set("手动输入模式")
            if PYNPUT_AVAILABLE:
                self.tips_var.set("请在下方输入坐标值，或点击'获取当前位置'，也可按 Ctrl 键锁定当前位置")
            else:
                self.tips_var.set("请在下方输入坐标值，或点击'获取当前位置'")
            self.manual_frame.grid()

            # 在手动模式下也保持键盘监听器运行，以支持Ctrl键锁定
            if self.has_focus and PYNPUT_AVAILABLE:
                self.start_keyboard_listener()

            # 更新按钮状态
            self.auto_mode_btn.state(['!pressed'])
            self.manual_mode_btn.state(['pressed'])

            # 填充当前坐标到手动输入框
            self.manual_x_var.set(str(self.current_x))
            self.manual_y_var.set(str(self.current_y))
        else:
            # 自动模式
            if self.has_focus:
                self.status_var.set("自动采集模式")
                if PYNPUT_AVAILABLE:
                    self.tips_var.set("移动鼠标到目标位置，按 Ctrl 键锁定坐标")
                else:
                    self.tips_var.set("使用'获取当前位置'按钮或手动输入坐标，在轮询模式下Ctrl键不可用")
            else:
                # X11环境下即使失去焦点也能正常工作
                if self.environment == 'x11' and PYNPUT_AVAILABLE:
                    self.status_var.set("自动采集模式 (后台)")
                    self.tips_var.set("移动鼠标到目标位置，按 Ctrl 键锁定坐标 (全局监听)")
                else:
                    self.status_var.set("等待窗口激活")
                    self.tips_var.set("请点击窗口以激活自动采集模式")

            self.manual_frame.grid_remove()

            # 更新按钮状态
            self.auto_mode_btn.state(['pressed'])
            self.manual_mode_btn.state(['!pressed'])

            if self.has_focus:
                self.start_keyboard_listener()

        # 恢复锁定状态和红色显示
        if was_locked:
            if locked_coords_text:
                self.locked_coords_var.set(locked_coords_text)
            else:
                # 如果文本为空，重新构建
                coords_text = f"X: {self.locked_x}, Y: {self.locked_y}"
                self.locked_coords_var.set(coords_text)
                _debug_print(f"重新构建锁定坐标文本: {coords_text}")

            self.locked_display.configure(style='LockedCoord.TLabel')
            _debug_print(f"模式切换后恢复锁定状态: {self.locked_coords_var.get()}")

            # 强制刷新UI
            self.root.update_idletasks()

    def on_manual_input_focus(self, event):
        """手动输入框获得焦点时，锁定手动模式"""
        if not self.manual_mode_locked:
            self.manual_mode_locked = True
            self.user_activated_manual = True
            self.update_mode_ui()

    def on_manual_input_blur(self, event):
        """手动输入框失去焦点"""
        pass

    def on_manual_input_change(self, *args):
        """手动输入框内容变化时的回调"""
        try:
            if self.manual_mode_locked:
                x_str = self.manual_x_var.get().strip()
                y_str = self.manual_y_var.get().strip()

                if x_str.isdigit() and y_str.isdigit():
                    x = int(x_str)
                    y = int(y_str)
                    self.current_x = x
                    self.current_y = y
                    self.coords_label_var.set(f"X: {x}, Y: {y}")
                elif x_str or y_str:
                    self.coords_label_var.set(f"X: {x_str or '?'}, Y: {y_str or '?'}")
        except Exception:
            pass

    def on_focus_in(self, event):
        """窗口获得焦点"""
        self.has_focus = True
        if not self.manual_mode_locked:
            self.update_mode_ui()

    def on_focus_out(self, event):
        """窗口失去焦点"""
        self.has_focus = False
        # 只有在Wayland环境或pynput不可用时才切换到手动模式
        # X11环境下pynput可以全局监听，不需要切换
        if not self.manual_mode_locked and (self.environment == 'wayland' or not PYNPUT_AVAILABLE):
            self.manual_mode_locked = True
            self.user_activated_manual = False
            self.update_mode_ui()
        elif not self.manual_mode_locked:
            # X11环境下只更新UI状态，但保持自动模式
            self.update_mode_ui()

    def get_mouse_position(self):
        """获取鼠标位置"""
        if self.environment == 'wayland':
            success, pos = get_mouse_position_xdotool()
            if success:
                if not self.tracking_method:
                    self.tracking_method = "xdotool"
                return pos
        else:
            success, pos = get_mouse_position_pynput()
            if success:
                if not self.tracking_method:
                    self.tracking_method = "pynput"
                return pos

        success, pos = get_mouse_position_pynput()
        if success:
            if not self.tracking_method:
                self.tracking_method = "pynput"
            return pos

        return None

    def initial_mouse_position_update(self):
        """初始化时更新鼠标位置"""
        pos = self.get_mouse_position()
        if pos:
            self.current_x, self.current_y = pos
            coords_text = f"X: {self.current_x}, Y: {self.current_y}"

            try:
                self.coords_label_var.set(coords_text)
                # 强制刷新UI
                self.root.update_idletasks()
                _debug_print(f"初始化时获取到鼠标位置: ({self.current_x}, {self.current_y})")
                _debug_print(f"初始化设置坐标显示: {coords_text}")
                _debug_print(f"coords_label_var 当前值: {self.coords_label_var.get()}")
            except Exception as e:
                _debug_print(f"❌ 设置坐标显示失败: {e}")
        else:
            _debug_print("初始化时无法获取鼠标位置")

    def start_tracking(self):
        """启动鼠标追踪"""
        self.is_tracking = True

        # 由于pynput鼠标监听器在某些环境下不工作，优先使用轮询模式
        if self.environment == 'wayland' or not PYNPUT_AVAILABLE:
            _debug_print("使用轮询模式：Wayland环境或pynput不可用")
            self.start_polling_tracking()
        else:
            # 在X11环境下也使用轮询模式，因为pynput鼠标监听器可能不稳定
            _debug_print("使用轮询模式：确保鼠标追踪的稳定性")
            self.start_polling_tracking()

        self.start_keyboard_listener()

    def check_mouse_tracking(self):
        """检查鼠标追踪是否正常工作"""
        if not hasattr(self, 'mouse_move_detected'):
            self.mouse_move_detected = False

        if not self.mouse_move_detected and self.is_tracking:
            _debug_print("⚠️ pynput鼠标监听器3秒内未检测到鼠标移动，切换到轮询模式")
            # 停止pynput鼠标监听器
            if self.mouse_listener:
                try:
                    self.mouse_listener.stop()
                except:
                    pass
            # 启动轮询模式
            self.start_polling_tracking()

    def start_polling_tracking(self):
        """启动轮询方式的鼠标追踪"""
        _debug_print("启动轮询方式的鼠标追踪")

        def tracking_loop():
            last_x, last_y = -1, -1
            _debug_print("轮询追踪线程开始运行")
            while self.is_tracking and not self.is_closing:
                try:
                    # 总是获取鼠标位置，即使已锁定也要跟踪
                    pos = self.get_mouse_position()
                    if pos:
                        x, y = pos
                        if x != last_x or y != last_y:
                            # 总是更新当前坐标
                            self.current_x = x
                            self.current_y = y

                            # 只有在未锁定时才更新显示
                            if not self.are_coords_locked:
                                self.root.after(0, lambda: self.coords_label_var.set(f"X: {x}, Y: {y}"))
                                # 只有在切换到手动模式时才更新手动输入框
                                if not self.has_focus and self.manual_mode_locked:
                                    self.root.after(0, lambda: self.manual_x_var.set(str(x)))
                                    self.root.after(0, lambda: self.manual_y_var.set(str(y)))
                            else:
                                _debug_print(f"轮询模式：鼠标移动到 ({x}, {y})，但坐标已锁定，不更新显示")
                            last_x, last_y = x, y
                    else:
                        _debug_print("轮询模式：无法获取鼠标位置")
                    time.sleep(0.05)
                except Exception as e:
                    _debug_print(f"轮询模式异常: {e}")
                    time.sleep(0.1)

        self.tracking_thread = threading.Thread(target=tracking_loop, daemon=True)
        self.tracking_thread.start()

    def start_mouse_listener(self):
        """启动pynput鼠标监听器"""
        _debug_print("尝试启动pynput鼠标监听器")

        if not PYNPUT_AVAILABLE:
            _debug_print("pynput不可用，切换到轮询模式")
            self.start_polling_tracking()
            return

        try:
            def on_move(x, y):
                _debug_print(f"🖱️ pynput检测到鼠标移动: ({x}, {y}), is_tracking: {self.is_tracking}")

                # 标记检测到鼠标移动
                self.mouse_move_detected = True

                if self.is_tracking:
                    # 总是更新当前坐标，即使已锁定也要跟踪鼠标位置
                    self.current_x = x
                    self.current_y = y

                    # 只有在未锁定时才更新显示的坐标
                    if not self.are_coords_locked:
                        _debug_print(f"更新坐标显示: X: {x}, Y: {y}")
                        self.root.after(0, lambda: self.coords_label_var.set(f"X: {x}, Y: {y}"))
                        # 只有在切换到手动模式时才更新手动输入框
                        if not self.has_focus and self.manual_mode_locked:
                            self.root.after(0, lambda: self.manual_x_var.set(str(x)))
                            self.root.after(0, lambda: self.manual_y_var.set(str(y)))
                    else:
                        _debug_print(f"鼠标移动到 ({x}, {y})，但坐标已锁定，不更新显示")

            self.mouse_listener = mouse.Listener(on_move=on_move)
            self.mouse_listener.start()
            _debug_print("✅ pynput鼠标监听器启动成功")
        except Exception as e:
            _debug_print(f"❌ pynput鼠标监听器启动失败: {e}，切换到轮询模式")
            self.start_polling_tracking()

    def start_keyboard_listener(self):
        """启动键盘监听器"""
        if not PYNPUT_AVAILABLE:
            _debug_print("pynput不可用，无法启动键盘监听器")
            return

        # 检查是否已经有运行中的监听器
        if self.keyboard_listener:
            _debug_print(f"键盘监听器对象存在: {self.keyboard_listener}")
            if hasattr(self.keyboard_listener, 'running'):
                _debug_print(f"键盘监听器running属性: {self.keyboard_listener.running}")
                if self.keyboard_listener.running:
                    _debug_print("键盘监听器已在运行中，跳过重复启动")
                    return
                else:
                    _debug_print("键盘监听器存在但未运行，将重新启动")
            else:
                _debug_print("键盘监听器没有running属性，将重新启动")
        else:
            _debug_print("键盘监听器对象不存在，将创建新的")

        # X11环境下可以全局监听，不需要检查焦点
        # Wayland环境下需要有焦点才能监听
        if self.environment == 'wayland' and not self.has_focus:
            _debug_print("Wayland环境下窗口未获得焦点，暂不启动键盘监听器")
            return

        self.stop_keyboard_listener()

        try:
            def on_press(key):
                try:
                    _debug_print(f"键盘事件: {key}")

                    # X11环境下全局监听，Wayland环境下需要检查焦点
                    should_process = True
                    if self.environment == 'wayland':
                        should_process = self.has_focus

                    _debug_print(f"should_process: {should_process}, 环境: {self.environment}, 焦点: {self.has_focus}")

                    if should_process and (key == keyboard.Key.ctrl_l or key == keyboard.Key.ctrl_r):
                        _debug_print(f"✅ 检测到Ctrl键按下: {key}, 准备调用toggle_lock")
                        # 使用root.after确保在主线程中执行
                        self.root.after(0, self.toggle_lock)
                        _debug_print(f"✅ 已调度toggle_lock到主线程")
                    else:
                        _debug_print(f"❌ 不处理此按键: {key}")
                except AttributeError as e:
                    _debug_print(f"键盘事件处理错误 (AttributeError): {e}")
                    pass
                except Exception as e:
                    _debug_print(f"键盘监听器内部错误: {e}")

            self.keyboard_listener = keyboard.Listener(on_press=on_press)
            self.keyboard_listener.start()
            _debug_print(f"键盘监听器已启动 (环境: {self.environment}, 焦点: {self.has_focus})")
        except Exception as e:
            _debug_print(f"启动键盘监听器失败: {e}")
            pass

    def stop_keyboard_listener(self):
        """停止键盘监听器"""
        if self.keyboard_listener and hasattr(self.keyboard_listener, 'running') and self.keyboard_listener.running:
            try:
                self.keyboard_listener.stop()
            except Exception:
                pass
        self.keyboard_listener = None

    def toggle_lock(self):
        """切换锁定状态"""
        _debug_print("🔥🔥🔥 toggle_lock 方法被调用 🔥🔥🔥")
        _debug_print(f"当前锁定状态: {self.are_coords_locked}")
        _debug_print(f"当前坐标: ({self.current_x}, {self.current_y})")
        try:
            if self.are_coords_locked:
                # 解锁
                self.are_coords_locked = False

                # 确保UI更新在主线程中执行
                def update_unlocked_ui():
                    self.locked_coords_var.set("未锁定")
                    self.locked_display.configure(style='Coord.TLabel')
                    _debug_print("UI已更新为正常解锁状态")

                self.root.after(0, update_unlocked_ui)
                self.show_message("坐标已解锁")
                _debug_print("坐标已解锁")
            else:
                # 锁定当前位置
                _debug_print(f"开始锁定流程，当前坐标: ({self.current_x}, {self.current_y})")

                # 强制获取最新的鼠标位置，即使当前坐标为0也要获取
                pos = self.get_mouse_position()
                _debug_print(f"get_mouse_position() 返回: {pos}")

                if pos:
                    self.current_x, self.current_y = pos
                    _debug_print(f"获取到最新鼠标位置: ({self.current_x}, {self.current_y})")
                elif self.current_x == 0 and self.current_y == 0:
                    # 如果当前坐标为0且无法获取鼠标位置，尝试其他方法
                    _debug_print("当前坐标为0且无法获取鼠标位置，尝试备用方法")
                    # 尝试使用不同的方法获取鼠标位置
                    success_pynput, pos_pynput = get_mouse_position_pynput()
                    success_xdotool, pos_xdotool = get_mouse_position_xdotool()

                    if success_pynput and pos_pynput:
                        self.current_x, self.current_y = pos_pynput
                        _debug_print(f"通过pynput获取到鼠标位置: ({self.current_x}, {self.current_y})")
                    elif success_xdotool and pos_xdotool:
                        self.current_x, self.current_y = pos_xdotool
                        _debug_print(f"通过xdotool获取到鼠标位置: ({self.current_x}, {self.current_y})")
                    else:
                        _debug_print("所有方法都无法获取鼠标位置，使用默认坐标 (100, 100)")
                        self.current_x, self.current_y = 100, 100
                else:
                    _debug_print(f"未能获取鼠标位置，使用当前坐标: ({self.current_x}, {self.current_y})")

                self.are_coords_locked = True
                self.locked_x = self.current_x
                self.locked_y = self.current_y
                _debug_print(f"设置锁定坐标: X={self.locked_x}, Y={self.locked_y}")

                # 直接更新UI（简化版本）
                coords_text = f"X: {self.locked_x}, Y: {self.locked_y}"
                _debug_print(f"准备更新UI，坐标文本: {coords_text}")

                try:
                    self.locked_coords_var.set(coords_text)
                    _debug_print(f"✅ 已设置locked_coords_var: {self.locked_coords_var.get()}")

                    self.locked_display.configure(style='LockedCoord.TLabel')
                    _debug_print(f"✅ 已设置样式为LockedCoord.TLabel")

                    # 强制刷新UI
                    self.root.update_idletasks()
                    _debug_print(f"✅ 已强制刷新UI")

                    # 验证更新结果
                    current_text = self.locked_coords_var.get()
                    current_style = self.locked_display.cget('style')
                    _debug_print(f"✅ 验证结果 - 文本: {current_text}, 样式: {current_style}")

                except Exception as ui_error:
                    _debug_print(f"❌ UI更新失败: {ui_error}")
                    import traceback
                    _debug_print(f"❌ 错误详情: {traceback.format_exc()}")
                self.show_message("坐标已锁定")
                _debug_print(f"坐标已锁定: X: {self.locked_x}, Y: {self.locked_y}")

        except Exception as e:
            _debug_print(f"切换锁定状态时发生错误: {e}")
            pass

    def manual_lock_coords(self):
        """手动锁定坐标"""
        try:
            x_str = self.manual_x_var.get().strip()
            y_str = self.manual_y_var.get().strip()

            if not x_str or not y_str:
                messagebox.showwarning("输入错误", "请输入完整的坐标值", parent=self.root)
                return

            x = int(x_str)
            y = int(y_str)

            if x < 0 or y < 0 or x > 9999 or y > 9999:
                messagebox.showwarning("坐标超出范围", "坐标值应在有效范围内", parent=self.root)
                return

            self.are_coords_locked = True
            self.locked_x = x
            self.locked_y = y
            self.locked_coords_var.set(f"X: {self.locked_x}, Y: {self.locked_y}")
            # 设置为红色样式
            self.locked_display.configure(style='LockedCoord.TLabel')
            self.show_message("手动坐标已锁定")
            _debug_print(f"手动锁定UI已更新为红色状态: X: {self.locked_x}, Y: {self.locked_y}")

        except ValueError:
            messagebox.showerror("输入错误", "请输入有效的数字", parent=self.root)
        except Exception as e:
            messagebox.showerror("错误", f"操作失败: {e}", parent=self.root)

    def get_current_position(self):
        """获取当前鼠标位置并填入输入框"""
        pos = self.get_mouse_position()
        if pos:
            x, y = pos
            self.manual_x_var.set(str(x))
            self.manual_y_var.set(str(y))
            self.show_message("已获取当前位置")
        else:
            messagebox.showwarning("获取失败", "无法获取当前位置", parent=self.root)

    def show_message(self, message):
        """显示状态消息"""
        original_message = self.tips_var.get()
        self.tips_var.set(f"✓ {message}")
        self.root.after(2000, lambda: self.tips_var.set(original_message))

    def confirm_capture(self):
        """确认捕获"""
        try:
            if self.is_closing:
                return

            name = self.name_var.get().strip()
            if not name:
                self.root.bell()
                messagebox.showwarning("输入错误", "请输入坐标名称！", parent=self.root)
                return

            final_x = self.locked_x if self.are_coords_locked else self.current_x
            final_y = self.locked_y if self.are_coords_locked else self.current_y

            if final_x == 0 and final_y == 0:
                pos = self.get_mouse_position()
                if pos:
                    final_x, final_y = pos

            result = {
                "name": name,
                "type": self.type_var.get(),
                "x": final_x,
                "y": final_y,
                "method": self.tracking_method or "auto",
                "environment": self.environment,
                "version": "professional",
                "locked": self.are_coords_locked,
                "input_method": "manual" if not self.has_focus and self.are_coords_locked else "auto",
                "timestamp": int(time.time() * 1000)
            }

            # 调试输出
            _debug_print(f"准备输出的结果对象: {result}")

            # 生成JSON字符串
            json_output = json.dumps(result)
            _debug_print(f"生成的JSON字符串: {json_output}")
            _debug_print(f"JSON字符串长度: {len(json_output)}")

            self.is_closing = True
            # 仅输出最终 JSON 到 stdout，一行即可
            _original_print(json_output)
            _debug_print("JSON已输出到stdout")
            sys.stdout.flush()
            self.root.after(100, self.cleanup_and_exit)

        except Exception as e:
            messagebox.showerror("错误", f"处理失败: {e}", parent=self.root)

    def cancel_capture(self):
        """取消捕获"""
        if self.is_closing:
            return

        _debug_print("用户取消捕获")
        self.is_closing = True
        _original_print("cancelled")
        _debug_print("已输出取消信号到stdout")
        sys.stdout.flush()
        self.root.after(100, self.cleanup_and_exit)

    def on_window_close(self):
        """窗口关闭"""
        self.cancel_capture()

    def cleanup_and_exit(self):
        """清理并退出"""
        _debug_print("开始清理资源")
        self.is_tracking = False

        self.stop_keyboard_listener()

        if self.mouse_listener and hasattr(self.mouse_listener, 'running') and self.mouse_listener.running:
            try:
                self.mouse_listener.stop()
            except Exception:
                pass

        try:
            if hasattr(self, 'tracking_thread') and self.tracking_thread.is_alive():
                self.tracking_thread.join(timeout=1)
        except Exception:
            pass

        sys.stdout.flush()
        sys.stderr.flush()

        try:
            if self.root and self.root.winfo_exists():
                self.root.destroy()
        except Exception:
            pass

        _debug_print("资源清理完成，进程即将退出")

if __name__ == "__main__":
    # 调试模式检查
    debug_mode = os.environ.get('GAT_POSITION_DEBUG')
    if debug_mode:
        _debug_print("启动POSITION坐标捕获工具（调试模式）")

    try:
        parser = argparse.ArgumentParser(description='Professional coordinate capture tool')
        parser.add_argument('--name', type=str, help='Initial name for the position.')
        parser.add_argument('--type', type=str, help='Initial type for the position.')
        args = parser.parse_args()

        _debug_print(f"命令行参数: name={args.name}, type={args.type}")

        root = tk.Tk()
        app = CoordinateCapture(root, initial_name=args.name, initial_type=args.type)

        _debug_print("开始Tkinter主循环")
        root.mainloop()
        _debug_print("Tkinter主循环结束")

    except KeyboardInterrupt:
        _debug_print("收到键盘中断信号")
        _original_print("cancelled")
        sys.exit(1)
    except Exception as e:
        _debug_print(f"发生异常: {e}")
        import traceback
        if debug_mode:
            traceback.print_exc(file=sys.stderr)
        sys.exit(1)
