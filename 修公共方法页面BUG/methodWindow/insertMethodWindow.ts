/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2023-2024 <PERSON><PERSON><PERSON>. All rights reserved.
 *  Licensed under the MIT License.
 *--------------------------------------------------------------------------------------------*/

import { Disposable, DisposableStore } from '../../../../../../base/common/lifecycle.js';
import { ILogService } from '../../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../../platform/notification/common/notification.js';
import { localize } from '../../../../../../nls.js';
import { Emitter } from '../../../../../../base/common/event.js';
import { IThemeService } from '../../../../../../platform/theme/common/themeService.js';
import { IFileService } from '../../../../../../platform/files/common/files.js';
import { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';
import { INativeEnvironmentService } from '../../../../../../platform/environment/common/environment.js';
import { INativeHostService } from '../../../../../../platform/native/common/native.js';
import { IWidgetCaptureService } from '../../../../../../platform/gat/common/widgetCaptureService.js';
import { IInstantiationService } from '../../../../../../platform/instantiation/common/instantiation.js';
import { IWebviewService } from '../../../../../../workbench/contrib/webview/browser/webview.js';
import { IWorkspaceContextService } from '../../../../../../platform/workspace/common/workspace.js';

import { MethodDataLoader } from './methodDataLoader.js';
import { ThemeManager } from './themeManager.js';
import { DragManager } from './dragManager.js';
import { MethodWindowUIBuilder } from './methodWindowUIBuilder.js';
import { MethodWindowEventHandler } from './methodWindowEventHandler.js';

/**
 * 插入公共方法窗口
 */
export class InsertMethodWindow extends Disposable {
    private readonly disposables = new DisposableStore();
    private methodWindow: Window | null = null;

    // fired when user confirms insertion or edit; isEdit indicates editing existing action
    private readonly _onMethodInserted = new Emitter<{ methodName: string, parameters: Record<string, any>, testCaseContext: any, isEdit: boolean, originalYAML?: string, testCaseFileUri?: string, originalStartLine?: number, originalEndLine?: number }>();
    public readonly onMethodInserted = this._onMethodInserted.event;

    // 添加捕获控件消息事件发射器
    private readonly _onCaptureControl = new Emitter<string>();
    public readonly onCaptureControl = this._onCaptureControl.event;

    // 组件
    private readonly methodDataLoader: MethodDataLoader;
    private readonly themeManager: ThemeManager;
    private readonly dragManager: DragManager;
    private readonly uiBuilder: MethodWindowUIBuilder;
    public readonly eventHandler: MethodWindowEventHandler;

    // 标记数据是否已加载
    private dataLoaded = false;

    // 测试用例上下文对象
    private testCaseContext: any = {};

    // 全局变量列表
    private globalVarList: Array<{ action: string; key: string; value: string }> = [];

    // 应用对象列表
    private appList: Array<{ label: string, value: string, en?: string, selected?: boolean }> = [];

    // 是否通过工具条调用，启用步骤选择器
    public stepSelectorEnabled: boolean = false;

    // 编辑上下文：用于编辑已有 Action 时预填方法名、参数和用例文件路径
    private editContext: { methodName: string; parametersYAML: string; testCaseFileUri?: string; originalStartLine?: number; originalEndLine?: number } | null = null;

    /**
     * 设置测试用例上下文
     */
    public setTestCaseContext(ctx: any): void {
        this.testCaseContext = ctx;
        this.logService.info(`设置测试用例上下文: ${JSON.stringify(ctx)}`);
        // 同步测试用例上下文到事件处理器，确保保存LOCATOR时可用
        this.eventHandler.setTestCaseContext(ctx);
    }

    public setDragMode(enable: boolean): void {
        if (this.uiBuilder) {
            this.uiBuilder.setDragMode(enable);
        }
    }

    /**
     * 设置应用对象列表
     */
    public setAppList(apps: Array<{ label: string, value: string, en?: string, selected?: boolean }>): void {
        this.appList = apps;
        this.logService.info(`设置应用对象列表，数量: ${apps.length}`);
        // 确保更新appList后立即同步到eventHandler
        if (this.eventHandler) {
            this.logService.info('立即将应用列表同步到eventHandler，长度:', apps.length);
            this.eventHandler.setAppList(apps);
        }
    }

    /**
     * 设置编辑上下文，记录被编辑块的行号
     */
    public setEditContext(methodName: string, parametersYAML: string, testCaseFileUri?: string, originalStartLine?: number, originalEndLine?: number): void {
        this.editContext = { methodName, parametersYAML, testCaseFileUri, originalStartLine, originalEndLine };
        this.logService.info(`已设置编辑上下文: 方法=${methodName}, 文件=${testCaseFileUri}, lines=${originalStartLine}-${originalEndLine}`);
    }

    constructor(
        @ILogService private readonly logService: ILogService,
        @INotificationService private readonly notificationService: INotificationService,
        @IThemeService themeService: IThemeService,
        @IFileService fileService: IFileService,
        @IConfigurationService configurationService: IConfigurationService,
        @INativeEnvironmentService environmentService: INativeEnvironmentService,
        @INativeHostService nativeHostService: INativeHostService,
        @IWidgetCaptureService widgetCaptureService: IWidgetCaptureService,
        @IInstantiationService instantiationService: IInstantiationService,
        @IWebviewService webviewService: IWebviewService,
        @IWorkspaceContextService workspaceContextService: IWorkspaceContextService
    ) {
        super();

        // 初始化组件
        this.methodDataLoader = new MethodDataLoader(logService, fileService, configurationService, workspaceContextService);
        this.themeManager = new ThemeManager(themeService, logService);
        this.dragManager = new DragManager(logService);
        this.uiBuilder = new MethodWindowUIBuilder(this.methodDataLoader, this.themeManager, logService);
        this.eventHandler = new MethodWindowEventHandler(
            this.methodDataLoader,
            logService,
            notificationService,
            widgetCaptureService,
            nativeHostService,
            fileService,
            environmentService,
            configurationService,
            webviewService,
            workspaceContextService
        );

        // 将组件添加到disposables
        this.disposables.add(this.methodDataLoader);
        this.disposables.add(this.themeManager);
        this.disposables.add(this.dragManager);
        this.disposables.add(this.uiBuilder);
        this.disposables.add(this.eventHandler);
    }

    // 静态标记预加载状态
    private static isPreloading = false;
    private static preloadPromise: Promise<void> | null = null;

    /**
     * 预加载方法数据和创建隐藏窗口
     * 优化版本：同时预加载数据和创建窗口
     */
    public async preloadMethodData(): Promise<void> {
        // 如果数据已加载，直接返回
        if (this.dataLoaded) {
            return;
        }

        // 如果正在预加载，等待完成
        if (InsertMethodWindow.isPreloading && InsertMethodWindow.preloadPromise) {
            this.logService.debug('等待正在进行的预加载完成');
            await InsertMethodWindow.preloadPromise;
            this.dataLoaded = true;
            return;
        }

        // 开始预加载
        InsertMethodWindow.isPreloading = true;
        InsertMethodWindow.preloadPromise = this._preloadMethodData();

        try {
            await InsertMethodWindow.preloadPromise;
            this.dataLoaded = true;
        } finally {
            InsertMethodWindow.isPreloading = false;
            InsertMethodWindow.preloadPromise = null;
        }
    }

    /**
     * 实际执行预加载的内部方法
     */
    private async _preloadMethodData(): Promise<void> {
        try {
            this.logService.info('开始预加载方法数据');

            // 只预加载方法数据，不创建隐藏窗口
            await this.methodDataLoader.loadMethodsData();

            this.logService.info(`方法数据预加载完成，共 ${this.methodDataLoader.getMethodCategories().size} 个类别`);
        } catch (error) {
            this.logService.error('预加载方法数据失败:', error);
            // 预加载失败不影响正常使用，只是会导致首次打开窗口时有延迟
        }
    }

    /**
     * 显示插入公共方法窗口
     * 优化版本：使用渐进式加载和预加载窗口
     */
    public async show(): Promise<void> {
        this.logService.info('显示插入公共方法窗口');

        try {
            // 先隐藏当前窗口（如果存在）
            this.hide();

            // 创建窗口并显示加载中提示
            await this.createWindowWithLoadingIndicator();

            // 确保数据已加载
            if (!this.dataLoaded) {
                // 在加载中提示的同时加载数据
                await this.methodDataLoader.loadMethodsData();
                this.dataLoaded = true;
            }

            // 确保窗口存在
            if (!this.methodWindow || this.methodWindow.closed) {
                throw new Error('窗口已关闭或不存在');
            }

            // 初始化可插入的全局参数
            this.initEventHandlerReturnVars();

            const doc = this.methodWindow.document;
            this.logService.info('创建UI内容');

            // 将测试用例上下文传递给 UI
            this.uiBuilder.setTestCaseContext(this.testCaseContext);
            // 注入到事件处理器，负责渲染应用对象下拉
            this.eventHandler.setTestCaseContext(this.testCaseContext);
            // 编辑模式时传递上下文，否则清除编辑状态
            if (this.editContext) {
                this.logService.info('进入编辑模式，设置 UI Builder 编辑上下文');
                this.uiBuilder.setEditMethodContext(this.editContext);
            } else {
                this.logService.info('非编辑模式，清除 UI Builder 编辑上下文');
                this.uiBuilder.clearEditMethodContext();
            }
            // 当外部未提供应用列表时，窗口内部加载app_menu.py
            if (!this.appList || this.appList.length === 0) {
                this.logService.info('应用列表为空，尝试窗口内部加载app_menu.py');
                await this.eventHandler.loadAppMenuClasses();
                const loadedApps = this.eventHandler.getAppList();
                this.logService.info('窗口内部加载应用列表成功，数量:', loadedApps.length);
                this.setAppList(loadedApps);
            }
            // 双重检查：确保应用列表被正确设置
            if (this.appList && this.appList.length > 0) {
                this.logService.info('在UI创建前再次确认appList已传递，长度:', this.appList.length);
                this.eventHandler.setAppList(this.appList);
            } else {
                this.logService.warn('警告：appList为空或未初始化');
            }
            // 记录默认选中步骤节点
            this.logService.info(`默认选中步骤节点(UI): ${this.uiBuilder.getSelectedSection()}`);
            this.uiBuilder.createWindowContent(doc);
            this.logService.info('UI内容创建完成');

            // 如果存在编辑上下文，预填方法详情
            if (this.editContext) {
                this.logService.info(`编辑 Action - 预填方法: ${this.editContext.methodName}`);
                this.eventHandler.updateMethodDetails(doc, this.editContext.methodName);
                // 解析并应用参数 YAML
                this.eventHandler.applyParameterYAML(doc, this.editContext.parametersYAML);
            }

            // 根据调用上下文启用或禁用步骤选择器
            const stepSelect = doc.getElementById('step-select') as HTMLSelectElement | null;
            if (stepSelect) {
                stepSelect.disabled = !this.stepSelectorEnabled;
            }

            // 设置方法窗口引用
            this.eventHandler.setMethodWindow(this.methodWindow);

            // 添加事件监听器
            this.logService.info('设置事件监听器');
            this.eventHandler.setupEventListeners(
                doc,
                (methodName: string, parameters: Record<string, any>) => {
                    // 打印传入的测试用例上下文对象，直接查看原始属性
                    this.logService.info(`插入公共方法时的 testCaseContext: ${JSON.stringify(this.testCaseContext, null, 2)}`);
                    // 打印编辑上下文信息
                    this.logService.info(`onMethodInserted 触发前: isEdit=${!!this.editContext}, testCaseFileUri=${this.editContext?.testCaseFileUri}`);
                    // 触发方法插入事件
                    this._onMethodInserted.fire({
                        methodName,
                        parameters,
                        testCaseContext: this.testCaseContext,
                        isEdit: !!this.editContext,
                        originalYAML: this.editContext?.parametersYAML,
                        testCaseFileUri: this.editContext?.testCaseFileUri,
                        originalStartLine: this.editContext?.originalStartLine,
                        originalEndLine: this.editContext?.originalEndLine
                    });
                    // 显示通知
                    this.notificationService.info(localize('methodInserted', "已插入方法: {0}", methodName));
                    // 更新状态提示栏
                    const statusDiv = doc.getElementById('insert-status');
                    if (statusDiv) {
                        const section = this.uiBuilder.getSelectedSection();
                        statusDiv.textContent = localize('methodInsertedStatus', "已向 {0} 插入方法: {1}", section, methodName);
                    }
                },
                () => this.hide()
            );

            // 监听捕获控件消息事件
            this.disposables.add(this.eventHandler.onCaptureControl(message => {
                this.logService.info(`接收到捕获控件消息: ${message}`);
                // 转发捕获控件消息
                this._onCaptureControl.fire(message);
            }));

            // 监听窗口关闭事件，仅隐藏窗口，保留实例以便后续重新打开时捕获事件可用
            this.methodWindow.addEventListener('beforeunload', () => {
                this.hide();
            });

            // 添加拖拽功能
            this.dragManager.setupDragFunctionality(doc, this.methodWindow);
            this.logService.info('插入公共方法窗口创建完成');

        } catch (error) {
            this.logService.error('打开插入公共方法窗口失败', error);
            this.notificationService.error(localize('openInsertMethodWindowError', "无法打开插入公共方法窗口: {0}", error));
        }
    }

    /**
     * 创建窗口并显示加载中提示
     */
    private async createWindowWithLoadingIndicator(): Promise<void> {
        // 创建新窗口
        this.logService.debug('创建新窗口');
        this.methodWindow = window.open('', 'insertMethodWindow', 'width=800,height=600,resizable=yes,toolbar=no,menubar=no,location=no,status=no,titlebar=no,dialog=yes');
        if (!this.methodWindow) {
            throw new Error('无法创建插入公共方法窗口，可能被浏览器拦截了');
        }

        // 将录制工具条窗口引用传递给新窗口
        try {
            if ((window as any).recordingToolbarWindow) {
                (this.methodWindow as any).recordingToolbarWindow = (window as any).recordingToolbarWindow;
                this.logService.info('已将录制工具条窗口引用传递给插入公共方法窗口');
            } else {
                this.logService.warn('无法获取录制工具条窗口引用');
            }
        } catch (error) {
            this.logService.error(`传递录制工具条窗口引用时出错: ${error}`);
        }

        // 设置窗口位置到屏幕中央
        const screenWidth = window.screen.width;
        const screenHeight = window.screen.height;
        const windowWidth = 800;
        const windowHeight = 600;
        const windowLeft = (screenWidth - windowWidth) / 2;
        const windowTop = (screenHeight - windowHeight) / 2;
        this.logService.debug(`设置窗口位置: left=${windowLeft}, top=${windowTop}`);
        this.dragManager.setWindowPosition(windowLeft, windowTop);
        this.methodWindow.moveTo(windowLeft, windowTop);
        this.methodWindow.resizeTo(windowWidth, windowHeight);

        // 等待窗口加载完成
        await new Promise<void>(resolve => {
            if (this.methodWindow) {
                this.methodWindow.onload = () => resolve();
                setTimeout(resolve, 1000); // 设置超时，防止无限等待
            } else {
                resolve();
            }
        });

        // 创建加载中提示元素
        if (this.methodWindow && !this.methodWindow.closed) {
            const doc = this.methodWindow.document;

            // 创建元素而不是使用 document.write
            const html = doc.createElement('html');
            const head = doc.createElement('head');
            const body = doc.createElement('body');

            // 添加标题
            const title = doc.createElement('title');
            title.textContent = '插入公共方法';
            head.appendChild(title);

            // 添加元数据
            const meta1 = doc.createElement('meta');
            meta1.setAttribute('charset', 'UTF-8');
            head.appendChild(meta1);

            const meta2 = doc.createElement('meta');
            meta2.setAttribute('name', 'viewport');
            meta2.setAttribute('content', 'width=device-width, initial-scale=1.0');
            head.appendChild(meta2);

            // 添加样式
            const style = doc.createElement('style');
            style.textContent = `
                body {
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
                    background-color: #f5f5f5;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                    margin: 0;
                    color: #333;
                }
                .loading-container {
                    text-align: center;
                }
                .spinner {
                    border: 4px solid rgba(0, 0, 0, 0.1);
                    border-radius: 50%;
                    border-top: 4px solid #3498db;
                    width: 40px;
                    height: 40px;
                    margin: 0 auto 20px;
                    animation: spin 1s linear infinite;
                }
                @keyframes spin {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
            `;
            head.appendChild(style);

            // 添加加载中内容
            const loadingContainer = doc.createElement('div');
            loadingContainer.className = 'loading-container';

            const spinner = doc.createElement('div');
            spinner.className = 'spinner';
            loadingContainer.appendChild(spinner);

            const loadingText = doc.createElement('p');
            loadingText.textContent = '正在加载公共方法数据...';
            loadingContainer.appendChild(loadingText);

            body.appendChild(loadingContainer);

            // 组装文档
            html.appendChild(head);
            html.appendChild(body);

            // 清空当前文档并添加新内容
            while (doc.firstChild) {
                doc.removeChild(doc.firstChild);
            }
            doc.appendChild(html);
        }
    }

    /**
     * 隐藏插入公共方法窗口
     */
    public hide(): void {
        if (this.methodWindow && !this.methodWindow.closed) {
            this.methodWindow.close();
        }
        this.methodWindow = null;
    }

    /**
     * 新增：获取 UI 选中的步骤节点
     */
    public getSelectedSection(): string {
        return this.uiBuilder.getSelectedSection();
    }

    /** 初始化可插入的全局参数 */
    public initGlobalReturnVar(fullYamlContent: string) {
        try {
            if (this.globalVarList.length > 0) {
                this.globalVarList.length = 0;
            }
            const yamlObject = (window as any).jsyaml.load(fullYamlContent) as Record<string, Record<string, any>>;
            if (!yamlObject) {
                return;
            }

            const findActions = (obj: any) => {
                if (Array.isArray(obj)) {
                    obj.forEach(item => findActions(item));
                } else if (typeof obj === 'object' && obj !== null) {
                    // 检查当前对象是否包含action
                    if (obj.action && obj?.kwargs) {
                        Object.keys(obj.kwargs).forEach(key => {
                            if (key.startsWith('return_')) {
                                this.globalVarList.push({
                                    action: obj.action,
                                    key,
                                    value: obj.kwargs[key],
                                });
                            }
                        });
                    }
                    // 递归遍历对象的所有属性
                    Object.values(obj).forEach(value => {
                        if (typeof value === 'object' && value !== null) {
                            findActions(value);
                        }
                    });
                }
            };

            Object.keys(yamlObject).forEach(key => {
                findActions(yamlObject[key]);
            });

            console.log('找到待设置的全局变量', JSON.stringify(this.globalVarList));
        } catch (error) {
            this.logService.error(`initInsertReturnVar解析YAML文件失败: ${error}`);
        }
    }

    /** 将全局参数添加到MethodWindowEventHandler上下文 */
    public initEventHandlerReturnVars() {
        if (this.globalVarList.length === 0) {
            return;
        }

        MethodWindowEventHandler.clearReturnVars();
        this.globalVarList.forEach(item => {
            const methodItem = this.methodDataLoader.getMethodDetail(item.action);
            if (!methodItem || !methodItem?.parameters) {
                return;
            }

            const paramItem = methodItem.parameters.find((param: any) => param.name === item.key);
            if (!paramItem) {
                return;
            }

            const desc = paramItem?.description || paramItem?.paramname || paramItem?.AdvancePramname;
            MethodWindowEventHandler.addReturnVar(item.value, desc);
        });
    }

    /**
     * 销毁窗口
     */
    override dispose(): void {
        this.hide();
        this.disposables.dispose();
        super.dispose();
    }
}
