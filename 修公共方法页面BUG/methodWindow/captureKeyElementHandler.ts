/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { ILogService } from '../../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../../platform/notification/common/notification.js';
import { localize } from '../../../../../../nls.js';
import { WidgetCaptureManager } from './widgetCapture/widgetCaptureManager.js';
import { IWidgetInfo } from './widgetCapture/types.js';
import { MessageDispatcher } from './messageDispatcher.js';
// import { getActiveWindow, computeScreenAwareSize } from '../../../../../../base/browser/dom.js';
import { mainWindow } from '../../../../../../base/browser/window.js';
import { IFileService } from '../../../../../../platform/files/common/files.js';
import { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';
import { URI } from '../../../../../../base/common/uri.js';
import { joinPath } from '../../../../../../base/common/resources.js';
import { VSBuffer } from '../../../../../../base/common/buffer.js';
import { IRegionCaptureResult } from './widgetCapture/regionCaptureService.js';
import { GATPathResolver } from '../../common/pathResolver.js';
import { IWorkspaceContextService } from '../../../../../../platform/workspace/common/workspace.js';

/**
 * 处理键/元素捕获的逻辑，将原 handleKeyElementCapture 代码封装到这里
 */
export class CaptureKeyElementHandler {
    private readonly pathResolver: GATPathResolver;

    constructor(
        private readonly dispatcher: MessageDispatcher,
        private readonly widgetCaptureManager: WidgetCaptureManager,
        private readonly notificationService: INotificationService,
        private readonly logService: ILogService,
        private readonly fileService: IFileService,
        private readonly configurationService: IConfigurationService,
        private readonly workspaceContextService: IWorkspaceContextService
    ) {
        // 初始化路径解析器
        this.pathResolver = new GATPathResolver(
            this.configurationService,
            this.workspaceContextService,
            this.fileService,
            this.logService
        );
    }

    public handle(methodWindow: Window, doc: Document, paramName: string, keyType: string): void {
        try {
            this.logService.info(`开始捕获${keyType}元素，参数: ${paramName}`);

            // 在进入捕获功能之前，根据元素类型自动同步type字段
            const typeInput = doc.getElementById('param-type') as HTMLInputElement;
            if (typeInput) {
                const oldValue = typeInput.value;
                let newValue = oldValue;

                // 根据keyType设置对应的type值
                if (keyType === 'UNI') {
                    newValue = 'UNI';
                } else if (keyType === 'GAT') {
                    newValue = 'GAT';
                } else if (keyType === 'ICON' || keyType === 'REGION' || keyType === 'POSITION' || keyType === 'HOTKEY') {
                    // ICON、REGION、POSITION、HOTKEY类型归属于OCR方式
                    newValue = 'OCR';
                } else {
                    // 其他类型默认为OCR
                    newValue = 'OCR';
                }

                // 只有当值需要改变时才更新
                if (oldValue !== newValue) {
                    this.logService.info(`自动同步type字段: ${oldValue} -> ${newValue} (基于元素类型: ${keyType})`);
                    typeInput.value = newValue;
                    // 触发change事件，确保相关逻辑能够响应
                    typeInput.dispatchEvent(new Event('change', { bubbles: true }));
                }
            } else {
                this.logService.warn('未找到type参数输入框，无法自动同步type字段');
            }

            // 保存当前方法窗口位置和大小
            const windowX = methodWindow.screenX;
            const windowY = methodWindow.screenY;
            const windowW = methodWindow.outerWidth;
            const windowH = methodWindow.outerHeight;
            this.logService.info(`保存窗口 geometry: x=${windowX}, y=${windowY}, w=${windowW}, h=${windowH}`);

            // 仅在非ICON捕获时隐藏方法窗口至最小化角落
            const hideWindowKeyTypes: string[] = ['ICON', 'IMAGE', 'HOTKEY'];
            if (!hideWindowKeyTypes.includes(keyType)) {
                try {
                    const sw = methodWindow.screen.availWidth;
                    const sh = methodWindow.screen.availHeight;
                    methodWindow.resizeTo(1, 1);
                    methodWindow.moveTo(sw, sh);
                    mainWindow.focus();
                    this.logService.info(`方法窗口已最小化: size=1x1, pos=${sw},${sh}`);
                } catch (err) {
                    this.logService.warn(`隐藏方法窗口失败: ${err}`);
                }
            }

            if (keyType === 'UNI') {
                const prompt = '请按下Ctrl+鼠标左键以选择控件';
                this.dispatcher.dispatchControl(prompt);
                this.logService.info('启动UNI控件捕获');

                this.widgetCaptureManager.startCapture('UNI', (widgetInfo: IWidgetInfo) => {
                    // 捕获完成后恢复窗口 geometry
                    if (methodWindow && !methodWindow.closed) {
                        try {
                            this.logService.info(`恢复窗口 geometry to x=${windowX}, y=${windowY}, w=${windowW}, h=${windowH}`);
                            methodWindow.resizeTo(windowW, windowH);
                            methodWindow.moveTo(windowX, windowY);
                            methodWindow.focus();
                        } catch (err) {
                            this.logService.error(`恢复方法窗口失败: ${err}`);
                        }
                    }

                    // 检查从Python脚本返回的详细信息，特别是datamap中的辅助状态
                    const datamap = widgetInfo.datamap as any; // 强制类型以便访问额外字段
                    let captureStatus = datamap?.capture_status; // fallback, error
                    let errorMessage = datamap?.error; // 具体的错误/警告信息

                    // 如果datamap本身就是错误对象（例如UNI.py直接返回错误时）
                    if (!captureStatus && widgetInfo.capture_status) {
                        captureStatus = widgetInfo.capture_status;
                        errorMessage = widgetInfo.error;
                    }

                    const keyInput = doc.querySelector(`#param-${paramName}`) as HTMLInputElement;
                    const datamapInput = doc.querySelector(`#param-${paramName}-datamap`) as HTMLInputElement;

                    if (captureStatus === 'error') {
                        this.logService.error(`控件捕获失败: ${errorMessage}`);
                        this.notificationService.error(localize('captureFailedError', "控件捕获失败: {0}", errorMessage || '未知错误'));
                        if (keyInput) keyInput.value = ''; // 清空输入框
                        if (datamapInput) datamapInput.value = '';
                        this.dispatcher.dispatchControl(`控件捕获失败: ${errorMessage || '未知错误'}`);
                        return; // 中断后续处理
                    }

                    if (captureStatus === 'fallback') {
                        this.logService.warn(`控件捕获回退: ${errorMessage}`);
                        this.notificationService.warn(localize('captureFallbackWarning', "控件捕获警告: {0}", errorMessage || '未能获取完整控件信息'));
                        // 即使是fallback，也填充信息，但用户已知晓
                    }

                    // 更新输入框
                    if (keyInput) {
                        // 优先使用 datamap.Key 作为控制名称，其次 fallback 到 widgetInfo.name
                        // 如果 captureStatus 是 error 或 fallback，并且 widgetInfo.name 已经是类似 UNI_Control_ 的，则优先使用errorMessage
                        let controlName = (datamap?.Key as string) || widgetInfo.name;
                        if (!controlName || (captureStatus && widgetInfo.name?.startsWith('UNI_Control_'))) {
                            controlName = captureStatus === 'error' ? (errorMessage || `UNI_Error_${Date.now()}`) :
                                captureStatus === 'fallback' ? (errorMessage || `UNI_Fallback_${Date.now()}`) :
                                    `UNI_Control_${Date.now()}`; // 最后的备用
                        }

                        keyInput.value = controlName;

                        // 保存完整 datamap
                        if (datamapInput) { datamapInput.value = JSON.stringify(widgetInfo); }
                        this.notificationService.info(localize('widgetCaptured', "已捕获控件: {0}", controlName));
                        this.dispatcher.dispatchControl(`已捕获控件: ${controlName}`);
                    } else {
                        this.logService.error('未找到参数输入框，无法更新控件信息。');
                        this.notificationService.error(localize('inputNotFound', '未找到参数输入框，无法更新。'));
                    }
                });
                return;
            } else if (keyType === 'ICON' || keyType === 'IMAGE') {
                const promptMsg = '请拖动鼠标选择截图区域';
                this.dispatcher.dispatchControl(promptMsg);
                this.logService.info('启动ICON截图捕获');
                const defaultDelay = this.configurationService.getValue<number>('gat.iconCaptureDelay') ?? 3;
                (async () => {
                    // 使用方法窗口的 document
                    const mwDoc = doc;
                    // 确保方法窗口在前
                    try { methodWindow.focus(); } catch { }
                    // 遮罩层
                    const modal = mwDoc.createElement('div');
                    modal.style.cssText = 'position:fixed;top:0;left:0;width:100%;height:100%;background:rgba(0,0,0,0.5);display:flex;align-items:center;justify-content:center;z-index:10000;';
                    // 弹窗盒子
                    const box = mwDoc.createElement('div');
                    box.style.cssText = 'background:#fff;padding:16px;border-radius:4px;max-width:280px;box-shadow:0 2px 8px rgba(0,0,0,0.2);';
                    // 文本和输入框
                    const label = mwDoc.createElement('div'); label.textContent = '请输入ICON捕获延迟时间（秒）';
                    const input = mwDoc.createElement('input'); input.type = 'number'; input.min = '0'; input.value = defaultDelay.toString(); input.style.cssText = 'width:60px;margin:8px 0;';
                    // 按钮容器
                    const btnWrap = mwDoc.createElement('div'); btnWrap.style.cssText = 'display:flex;justify-content:flex-end;gap:8px;';
                    const btnCancel = mwDoc.createElement('button'); btnCancel.textContent = '取消';
                    const btnOk = mwDoc.createElement('button'); btnOk.textContent = '确认';
                    btnWrap.appendChild(btnCancel); btnWrap.appendChild(btnOk);
                    // 组装
                    box.appendChild(label); box.appendChild(input); box.appendChild(btnWrap);
                    modal.appendChild(box); mwDoc.body.appendChild(modal);
                    // 等待用户输入
                    const delay = await new Promise<number>(resolve => {
                        btnOk.addEventListener('click', () => resolve(parseInt(input.value, 10) >= 0 ? parseInt(input.value, 10) : defaultDelay));
                        btnCancel.addEventListener('click', () => resolve(defaultDelay));
                    });
                    mwDoc.body.removeChild(modal);
                    this.logService.info(`ICON 捕获延迟: ${delay}s`);
                    // ICON捕获前隐藏方法窗口
                    try {
                        const sw = methodWindow.screen.availWidth;
                        const sh = methodWindow.screen.availHeight;
                        methodWindow.resizeTo(1, 1);
                        methodWindow.moveTo(sw, sh);
                        mainWindow.focus();
                        this.logService.info(`ICON前方法窗口已最小化: size=1x1, pos=${sw},${sh}`);
                    } catch (err) {
                        this.logService.warn(`ICON前隐藏方法窗口失败: ${err}`);
                    }
                    // 直接调用截图捕获，并传递延迟秒数
                    this.logService.info(`准备开始ICON捕获，参数名: ${paramName}`);
                    this.widgetCaptureManager.startCapture(
                        'ICON',
                        (alias: string) => {
                            this.logService.info(`ICON捕获回调: ${alias}`);
                            // 恢复窗口 geometry
                            if (methodWindow && !methodWindow.closed) {
                                try {
                                    this.logService.info(`ICON 结束恢复窗口 geometry to x=${windowX}, y=${windowY}, w=${windowW}, h=${windowH}`);
                                    methodWindow.resizeTo(windowW, windowH);
                                    methodWindow.moveTo(windowX, windowY);
                                    methodWindow.focus();
                                } catch (err) {
                                    this.logService.error(`ICON 结束恢复方法窗口失败: ${err}`);
                                }
                            }
                            // 更新输入框
                            const keyInput = doc.querySelector(`#param-${paramName}`) as HTMLInputElement;
                            if (keyInput) {
                                keyInput.value = alias;
                                const datamap = doc.querySelector(`#param-${paramName}-datamap`) as HTMLInputElement;
                                if (datamap) { datamap.value = alias; }
                                this.notificationService.info(localize('widgetCapturedIcon', '已捕获截图: {0}', alias));
                                this.dispatcher.dispatchControl(`已捕获截图: ${alias}`);
                            }
                        },
                        delay
                    );
                })();
                return;
            } else if (keyType === 'REGION' || keyType === 'TEXT') {
                this.dispatcher.dispatchControl(localize('widgetCapture.startRegion', '已进入区域捕获模式，请拖动鼠标选择区域，按ESC取消'));
                this.logService.info('启动REGION区域捕获');
                this.widgetCaptureManager.startCapture('REGION', (res: IRegionCaptureResult) => {
                    // 恢复窗口 geometry
                    if (methodWindow && !methodWindow.closed) {
                        try { methodWindow.resizeTo(windowW, windowH); methodWindow.moveTo(windowX, windowY); methodWindow.focus(); }
                        catch (err) { this.logService.error(`REGION 结束恢复方法窗口失败: ${err}`); }
                    }
                    // 更新输入框
                    const keyInput = doc.querySelector(`#param-${paramName}`) as HTMLInputElement;
                    if (keyInput) { keyInput.value = res.element_name; }
                    const datamapInput = doc.querySelector(`#param-${paramName}-datamap`) as HTMLInputElement;
                    if (datamapInput) {
                        // 保存完整捕获结果
                        datamapInput.value = JSON.stringify(res);
                    }
                    this.notificationService.info(localize('regionCaptured', '已捕获区域: {0}', res.element_name));
                    this.dispatcher.dispatchControl(`已捕获区域: ${res.element_name}`);
                });
                return;
            } else if (keyType === 'POSITION') {
                const prompt = '正在打开鼠标位置获取窗口';
                this.dispatcher.dispatchControl(prompt);
                this.logService.info('启动POSITION位置获取窗口');

                // 使用widgetCaptureManager处理POSITION捕获
                this.widgetCaptureManager.startCapture('POSITION', (positionInfo: any) => {
                    try {
                        // 检查是否为取消操作
                        if (positionInfo && positionInfo.cancelled) {
                            this.logService.info('POSITION位置捕获被取消');
                            this.dispatcher.dispatchControl('位置捕获已取消');

                            // 取消时也要恢复方法窗口
                            this.restoreMethodWindow(methodWindow, windowX, windowY, windowW, windowH, 'POSITION取消');
                            return;
                        }

                        this.logService.info(`接收到位置信息: ${JSON.stringify(positionInfo)}`);

                        // 更新参数输入框
                        // 确保positionInfo有正确的格式
                        if (!positionInfo) {
                            this.logService.error('位置信息为空');
                            return;
                        }

                        // 标准化positionInfo，确保格式一致
                        let normalizedInfo = positionInfo;

                        // 检查是否需要标准化
                        if (!normalizedInfo.name || !normalizedInfo.coords) {
                            this.logService.info('位置信息需要标准化处理');
                            // 创建标准化的位置信息
                            normalizedInfo = {
                                name: positionInfo.name || `位置_${Date.now()}`,
                                coords: {
                                    x: positionInfo.x || (positionInfo.coords?.x) || 0,
                                    y: positionInfo.y || (positionInfo.coords?.y) || 0
                                }
                            };
                            this.logService.info(`标准化后的位置信息: ${JSON.stringify(normalizedInfo)}`);
                        }

                        // 更新参数输入框
                        const keyInput = doc.querySelector(`#param-${paramName}`) as HTMLInputElement;
                        if (keyInput) {
                            // 更新参数输入框为元素名称
                            const name = normalizedInfo.name;
                            this.logService.info(`更新输入框值为元素名称: ${name}`);
                            keyInput.value = name;
                            // 触发输入框change事件
                            keyInput.dispatchEvent(new Event('change', { bubbles: true }));
                            this.logService.info('已触发参数输入框的change事件');

                            // 尝试更新元素对象名称输入框
                            try {
                                const nameInput = doc.querySelector('#element-name') as HTMLInputElement;
                                if (nameInput) {
                                    nameInput.value = normalizedInfo.name;
                                    this.logService.info(`已更新元素对象名称为: ${normalizedInfo.name}`);
                                    // 触发change事件
                                    nameInput.dispatchEvent(new Event('change', { bubbles: true }));
                                } else {
                                    this.logService.warn('未找到元素对象名称输入框');
                                }
                            } catch (nameError) {
                                this.logService.error(`更新元素对象名称失败: ${nameError}`);
                            }

                            // 更新隐藏 datamap 输入，存储完整位置 JSON
                            try {
                                const datamapInput = doc.querySelector(`#param-${paramName}-datamap`) as HTMLInputElement;
                                if (datamapInput) {
                                    datamapInput.value = JSON.stringify(normalizedInfo);
                                    this.logService.info(`已更新隐藏datamap输入: param-${paramName}-datamap`);
                                }
                            } catch (dmErr) {
                                this.logService.warn(`更新隐藏datamap输入失败: ${dmErr}`);
                            }
                        } else {
                            this.logService.error(`找不到参数输入框: #param-${paramName}`);
                        }
                    } catch (error) {
                        this.logService.error(`处理位置信息失败: ${error}`);
                    }

                    // 通知用户 (只在成功时通知)
                    if (positionInfo.name && positionInfo.coords) {
                        this.notificationService.info(localize('positionCaptured', "已捕获位置: {0} ({1}, {2})",
                            positionInfo.name, positionInfo.coords.x, positionInfo.coords.y));
                        this.dispatcher.dispatchControl(`已捕获位置: ${positionInfo.name} (${positionInfo.coords.x}, ${positionInfo.coords.y})`);
                    }

                    // 恢复方法窗口并聚焦
                    this.restoreMethodWindow(methodWindow, windowX, windowY, windowW, windowH, 'POSITION成功');
                });

                return;
            } else if (keyType === 'HOTKEY') {
                this.logService.info('启动HOTKEY快捷键输入');
                this.dispatcher.dispatchControl('请输入快捷键信息');

                // 恢复方法窗口
                this.restoreMethodWindow(methodWindow, windowX, windowY, windowW, windowH, 'HOTKEY输入');

                // 创建快捷键输入对话框
                this.createHotkeyInputDialog(methodWindow, doc, paramName);
                return;
            } else {
                const unsupported = localize('widgetCapture.typeNotSupported', "{0}类型的控件捕获功能正在开发中", keyType);
                this.logService.info(unsupported);
                this.dispatcher.dispatchControl(unsupported);
            }
        } catch (error) {
            this.logService.error(`处理控件捕获失败: ${error}`);
            this.notificationService.error(localize('captureElementFailed', "处理控件捕获失败: {0}", error));
        }
    }

    /**
     * 保存UNI控件到locator目录
     * @param widgetInfo UNI控件信息
     * @param driver 应用名称
     */
    public async saveUniLocator(widgetInfo: IWidgetInfo, driver: string): Promise<void> {
        try {
            // 获取测试用例目录配置
            const testCasePathFromConfig = this.configurationService.getValue<string>('gat.testcasePath');
            if (!testCasePathFromConfig) {
                throw new Error('未配置 gat.testcasePath');
            }

            // 确定locator目录路径（与testcase同级）
            const locatorDirPath = joinPath(URI.file(testCasePathFromConfig), '..', 'locator');
            this.logService.info(`locator目录路径: ${locatorDirPath.toString()}`);

            // 确保locator目录存在
            try {
                await this.fileService.createFolder(locatorDirPath);
                this.logService.info('已确保locator目录存在');
            } catch (error) {
                if (error instanceof Error && error.name !== 'FileExistsError') {
                    throw error;
                }
                // 如果目录已存在，忽略错误
            }

            // 生成控件唯一标识符
            const key = widgetInfo.datamap?.Key as string || `N${widgetInfo.name || 'UNI'}-DNA-P${Date.now()}`;

            // 创建YAML格式的内容 - 使用JSON风格格式匹配示例
            // 目标格式: key: { datamap: { 相关字段 } }
            const datamap = widgetInfo.datamap || {};
            // 使用自定义格式化函数生成无引号的JSON风格格式
            const datamapFormatted = this.formatObjectAsJsonStyle(datamap, 2);
            const yamlContent = `${key}:\n  datamap: ${datamapFormatted}\n`;

            // 构建文件名
            const fileName = `${driver}_uni.yml`;
            const filePath = joinPath(locatorDirPath, fileName);

            // 检查文件是否已存在
            let finalContent = yamlContent;
            try {
                if (await this.fileService.exists(filePath)) {
                    // 如果文件已存在，读取内容并追加
                    const existingContent = (await this.fileService.readFile(filePath)).value.toString();
                    // 检查是否已包含相同key
                    if (!existingContent.includes(key + ':')) {
                        finalContent = existingContent + '\n' + yamlContent;
                    } else {
                        this.logService.info(`控件 ${key} 已存在于 ${fileName} 中，不再追加`);
                        return;
                    }
                }
            } catch (error) {
                // 如果文件不存在，会抛出异常，这是预期行为，继续创建新文件
                this.logService.info(`创建新文件: ${filePath.toString()}`);
            }

            // 写入文件
            await this.fileService.writeFile(filePath, VSBuffer.fromString(finalContent));
            this.logService.info(`已保存UNI控件到文件: ${filePath.toString()}`);

            // 显示通知
            this.notificationService.info(localize('uniLocatorSaved', "已保存UNI控件到 {0}", fileName));
        } catch (error) {
            this.logService.error(`保存UNI控件到locator目录失败: ${error}`);
            throw error;
        }
    }

    /**
     * 保存ICON截图到locator目录
     * @param alias 元素名称
     * @param driver 应用名称
     * @param testCaseId 测试用例ID
     */
    public async saveIconLocator(alias: string, driver: string, testCaseId?: string): Promise<void> {
        try {
            const testCasePathFromConfig = this.configurationService.getValue<string>('gat.testcasePath');
            this.logService.info(`gat.testcasePath 配置值: ${testCasePathFromConfig}`);
            if (!testCasePathFromConfig) {
                throw new Error('未配置 gat.testcasePath');
            }
            const testCasePathUri = URI.file(testCasePathFromConfig.trim());
            // 创建 testdata 目录
            const testdataDir = joinPath(testCasePathUri, '..', 'testdata');
            this.logService.info(`testdata 目录 URI: ${testdataDir.toString()}`);
            await this.fileService.createFolder(testdataDir);
            // 创建 driver 和 testCaseId 目录
            const driverDir = joinPath(testdataDir, driver);
            this.logService.info(`driver 目录 URI: ${driverDir.toString()}`);
            await this.fileService.createFolder(driverDir);
            const caseIdUsed = testCaseId || `${Date.now()}`;
            this.logService.info(`使用的 caseId: ${caseIdUsed}`);
            const caseDir = joinPath(driverDir, caseIdUsed);
            this.logService.info(`case 目录 URI: ${caseDir.toString()}`);
            await this.fileService.createFolder(caseDir);

            // 拷贝截图文件
            const srcUri = URI.file(`/tmp/${alias}.png`);
            this.logService.info(`源截图文件 URI: ${srcUri.toString()}`);
            const destUri = joinPath(caseDir, `${alias}.png`);
            this.logService.info(`目标截图文件 URI: ${destUri.toString()}`);
            try {
                await this.fileService.copy(srcUri, destUri, true);
            } catch {
                this.logService.info(`覆盖已存在的截图文件: ${destUri.toString()}`);
                await this.fileService.copy(srcUri, destUri, true);
            }

            // 更新 locator YAML
            const locatorDir = joinPath(testCasePathUri, '..', 'locator');
            this.logService.info(`locator 目录 URI: ${locatorDir.toString()}`);
            await this.fileService.createFolder(locatorDir);
            const fileName = `${driver}.yml`;
            const locatorFile = joinPath(locatorDir, fileName);
            this.logService.info(`locator 文件 URI: ${locatorFile.toString()}`);
            const iconNode = `${alias}:\n  type: icon\n  icon:\n    - testdata/${driver}/${caseIdUsed}/${alias}.png\n`;
            this.logService.info(`待写入 locator 节点内容: ${iconNode.replace(/\n/g, '\\n')}`);
            let finalContent = iconNode;
            if (await this.fileService.exists(locatorFile)) {
                // 如果文件已存在，读取内容并追加
                const existingContent = (await this.fileService.readFile(locatorFile)).value.toString();
                // 检查是否已包含相同key
                if (!existingContent.includes(`${alias}:`)) {
                    finalContent = existingContent + '\n' + iconNode;
                } else {
                    this.logService.info(`截图 ${alias} 已存在于 ${fileName} 中，不再追加`);
                    return;
                }
            }
            await this.fileService.writeFile(locatorFile, VSBuffer.fromString(finalContent));
            this.notificationService.info(localize('iconLocatorSaved', "已保存截图 {0} 到 {1}", alias, fileName));
        } catch (error) {
            this.logService.error(`保存ICON截图到locator目录失败: ${error}`);
            throw error;
        }
    }

    /**
     * 保存位置信息到locator目录
     * @param positionInfo 位置信息对象
     * @param driver 应用名称
     */
    public async savePositionLocator(positionInfo: any, driver: string): Promise<void> {
        // 添加对key名按_分割的支持
        try {
            if (!positionInfo || !positionInfo.name || !positionInfo.coords) {
                throw new Error('无效的位置信息');
            }

            // 确保坐标是数字类型
            const coords = {
                x: typeof positionInfo.coords.x === 'string' ? parseInt(positionInfo.coords.x, 10) : positionInfo.coords.x,
                y: typeof positionInfo.coords.y === 'string' ? parseInt(positionInfo.coords.y, 10) : positionInfo.coords.y
            };

            this.logService.info(`调用 savePositionLocator: ${positionInfo.name}, 坐标: (${coords.x}, ${coords.y}), 驱动: ${driver}`);

            // 验证坐标有效性
            if (isNaN(coords.x) || isNaN(coords.y)) {
                throw new Error('无效的坐标值');
            }

            // 使用路径解析器获取 KylinRobot-v2 根目录
            const rootPath = await this.pathResolver.getKylinRobotRootPath();
            if (!rootPath) {
                throw new Error('无法获取有效的 KylinRobot-v2 根目录');
            }

            // 确定locator目录路径（在根目录下）
            const locatorDirPath = joinPath(rootPath, 'locator');
            this.logService.info(`locator目录路径: ${locatorDirPath.toString()}`);

            // 确保locator目录存在
            try {
                await this.fileService.createFolder(locatorDirPath);
                this.logService.info('已确保locator目录存在');
            } catch (error) {
                if (error instanceof Error && error.name !== 'FileExistsError') {
                    throw error;
                }
                // 如果目录已存在，忽略错误
            }

            // 生成位置唯一标识符
            const key = positionInfo.name;
            const actualType = typeof positionInfo.type === 'string' && positionInfo.type.trim() !== '' ? positionInfo.type : 'blank';

            // 检查key是否包含下划线，需要创建嵌套结构
            const hasNestedStructure = key.includes('_');

            // 创建YAML格式的内容
            let yamlContent = '';

            if (hasNestedStructure) {
                // 按_分割key，创建嵌套结构
                const segments = key.split('_');

                // 创建嵌套的YAML结构
                let indentation = '';
                let currentPath = '';

                // 构建嵌套路径
                for (let i = 0; i < segments.length - 1; i++) {
                    const segment = segments[i];
                    if (currentPath) {
                        currentPath += '_' + segment;
                    } else {
                        currentPath = segment;
                    }

                    yamlContent += `${indentation}${segment}:\n`;
                    indentation += '  ';
                }

                // 添加最后一个节点（包含实际值）
                const lastSegment = segments[segments.length - 1];
                yamlContent += `${indentation}${lastSegment}:\n`;
                indentation += '  ';

                // 添加类型和位置信息
                yamlContent += `${indentation}type: ${actualType}\n`;
                yamlContent += `${indentation}position:\n`;
                yamlContent += `${indentation}  x: ${coords.x}\n`;
                yamlContent += `${indentation}  y: ${coords.y}\n`;
            } else {
                // 创建普通YAML格式的内容（包含 type: blank 和 position）
                // 目标格式: key: { type: blank, position: { x: 123, y: 456 } }
                yamlContent = `${key}:\n  type: ${actualType}\n  position:\n    x: ${coords.x}\n    y: ${coords.y}\n`;
            }

            // 构建文件名（与 ICON、UNI 共用同一 YAML 文件）
            const fileName = `${driver}.yml`;
            const filePath = joinPath(locatorDirPath, fileName);

            // 检查文件是否已存在
            let finalContent = yamlContent;
            try {
                if (await this.fileService.exists(filePath)) {
                    // 如果文件已存在，读取内容并追加
                    const existingContent = (await this.fileService.readFile(filePath)).value.toString();
                    // 检查是否属于嵌套结构
                    if (hasNestedStructure) {
                        // 处理嵌套结构的更新
                        this.logService.info(`尝试更新嵌套结构节点: ${key}`);

                        // 按_分割key
                        const segments = key.split('_');
                        const topLevelKey = segments[0];

                        // 将YAML内容转换为行的数组，并保留缩进
                        const lines = existingContent.split('\n');

                        // 首先判断顶层节点是否存在
                        let topLevelFound = false;
                        let topLevelLineIndex = -1;

                        for (let i = 0; i < lines.length; i++) {
                            if (lines[i].trim() === `${topLevelKey}:` || lines[i].match(new RegExp(`^${topLevelKey}:($|\s)`))) {
                                topLevelFound = true;
                                topLevelLineIndex = i;
                                break;
                            }
                        }

                        // 根据顶层节点存在与否决定添加或合并方式
                        if (!topLevelFound) {
                            // 如果顶层节点不存在，直接追加新的嵌套结构
                            finalContent = existingContent + '\n' + yamlContent;
                            this.logService.info(`顶层节点 ${topLevelKey} 不存在，追加新的嵌套结构`);
                        } else {
                            // 顶层节点存在，需要处理嵌套结构
                            this.logService.info(`顶层节点 ${topLevelKey} 存在，尝试合并嵌套结构`);

                            // 分析当前顶层节点的缩进级别
                            const topLineIndent = lines[topLevelLineIndex].length - lines[topLevelLineIndex].trimLeft().length;

                            // 按原有YAML结构重新构造新的嵌套结构
                            let newNestedContent = '';
                            // 计算子节点缩进级别(每个子节点缩进增加2个空格)

                            // 如果有多于1个节点，需要构造中间节点
                            if (segments.length > 2) {
                                // 从第二个节点开始，到倒数第二个节点结束
                                for (let i = 1; i < segments.length - 1; i++) {
                                    const segment = segments[i];
                                    const segmentIndent = topLineIndent + (i * 2);
                                    newNestedContent += ' '.repeat(segmentIndent) + `${segment}:\n`;
                                }
                            }

                            // 添加最后一个节点及其内容
                            const lastSegment = segments[segments.length - 1];
                            const lastSegmentIndent = topLineIndent + ((segments.length - 1) * 2);
                            newNestedContent += ' '.repeat(lastSegmentIndent) + `${lastSegment}:\n`;
                            newNestedContent += ' '.repeat(lastSegmentIndent + 2) + `type: ${actualType}\n`;
                            newNestedContent += ' '.repeat(lastSegmentIndent + 2) + `position:\n`;
                            newNestedContent += ' '.repeat(lastSegmentIndent + 4) + `x: ${coords.x}\n`;
                            newNestedContent += ' '.repeat(lastSegmentIndent + 4) + `y: ${coords.y}\n`;

                            // 查找顶层节点的结束位置，将新内容插入到其中
                            let insertIndex = topLevelLineIndex + 1;
                            let foundEnd = false;

                            // 从顶层节点的下一行开始遍历
                            for (let i = topLevelLineIndex + 1; i < lines.length; i++) {
                                const lineIndent = lines[i].length - lines[i].trimLeft().length;

                                // 如果缩进小于等于顶层节点的缩进，说明已经走出了该节点范围
                                if (lineIndent <= topLineIndent && lines[i].trim().length > 0) {
                                    insertIndex = i;
                                    foundEnd = true;
                                    break;
                                }
                            }

                            // 如果没有找到结束位置，则在文件末尾插入
                            if (!foundEnd) {
                                insertIndex = lines.length;
                            }

                            // 将新内容插入到适当位置
                            // 注意：这里不能直接用splice，因为newNestedContent是多行字符串
                            const newLines = newNestedContent.split('\n');
                            // 过滤掉空行
                            const filteredNewLines = newLines.filter(line => line.trim() !== '');

                            // 将新行插入到适当位置
                            for (let i = 0; i < filteredNewLines.length; i++) {
                                lines.splice(insertIndex + i, 0, filteredNewLines[i]);
                            }

                            finalContent = lines.join('\n');

                            this.logService.info(`成功将嵌套节点 ${key} 合并到现有的 ${topLevelKey} 节点下`);
                        }
                    } else {
                        // 非嵌套结构的原有处理逻辑
                        // 检查是否已包含相同key
                        if (!existingContent.includes(key + ':')) {
                            finalContent = existingContent + '\n' + yamlContent;
                        } else {
                            this.logService.info(`位置 ${key} 已存在于 ${fileName} 中，将更新坐标值`);
                            const lines = existingContent.split('\n');
                            let inKeySection = false;
                            let updated = false;

                            for (let i = 0; i < lines.length; i++) {
                                if (lines[i].trim() === `${key}:`) {
                                    inKeySection = true;
                                } else if (inKeySection && lines[i].trim().startsWith('type:')) {
                                    // Potentially update type if logic requires
                                    // For now, just ensuring we identify it. If type needs update, change here.
                                    // lines[i] = `  type: ${actualType}`;
                                } else if (inKeySection && lines[i].trim() === 'position:') {
                                    // 找到position部分，更新x和y坐标
                                    if (i + 2 < lines.length &&
                                        lines[i + 1].trim().startsWith('x:') &&
                                        lines[i + 2].trim().startsWith('y:')) {
                                        lines[i + 1] = `    x: ${coords.x}`;
                                        lines[i + 2] = `    y: ${coords.y}`;
                                        updated = true;
                                        break;
                                    }
                                } else if (inKeySection && lines[i].trim() && !lines[i].trim().startsWith(' ')) {
                                    // 找到下一个顶级元素，表示key部分结束
                                    inKeySection = false;
                                }
                            }

                            if (updated) {
                                finalContent = lines.join('\n');
                            } else {
                                // If not updated (e.g., key existed but no 'position:' or coords, or structure was different)
                                // Append new content to be safe, or handle more complex merge/replace logic if needed.
                                // For now, let's assume if not updated cleanly, we append.
                                // A more robust solution might involve parsing YAML properly.
                                this.logService.warn(`位置 ${key} 在 ${fileName} 中结构不符合预期或未找到更新点，将尝试追加。`);
                                finalContent = existingContent + '\n' + yamlContent;
                            }
                        }
                    }
                }
            } catch (error) {
                // 如果文件不存在，会抛出异常，这是预期行为，继续创建新文件
                this.logService.info(`创建新文件: ${filePath.toString()}`);
            }

            // 写入文件
            await this.fileService.writeFile(filePath, VSBuffer.fromString(finalContent));
            this.logService.info(`已保存位置信息到文件: ${filePath.toString()}`);

            // 显示通知
            this.notificationService.info(localize('positionLocatorSaved', "已保存位置信息到 {0}", fileName));
        } catch (error) {
            this.logService.error(`保存位置信息到locator目录失败: ${error}`);
            throw error;
        }
    }

    /**
     * 保存区域信息到locator目录
     * @param info 区域信息对象, 包含 element_name
     * @param driver 应用名称
     */
    public async saveRegionLocator(info: IRegionCaptureResult, driver: string): Promise<void> {
        try {
            if (!info || !info.element_name) {
                throw new Error('无效的区域信息，缺少 element_name');
            }

            const { element_name, left_top_x, left_top_y, right_down_x, right_down_y } = info;

            this.logService.info(`调用 saveRegionLocator: ${element_name}, 坐标: (${left_top_x},${left_top_y}) 到 (${right_down_x},${right_down_y}), 驱动: ${driver}`);

            // 验证坐标有效性
            if (isNaN(left_top_x) || isNaN(left_top_y) || isNaN(right_down_x) || isNaN(right_down_y)) {
                throw new Error('无效的区域坐标值');
            }

            const testCasePath = this.configurationService.getValue<string>('gat.testcasePath');
            if (!testCasePath) {
                throw new Error('未配置 gat.testcasePath');
            }
            const locatorDirPath = joinPath(URI.file(testCasePath), '..', 'locator');

            try {
                await this.fileService.createFolder(locatorDirPath);
                this.logService.info('已确保locator目录存在');
            } catch (error) {
                if (error instanceof Error && error.name !== 'FileExistsError') {
                    throw error;
                }
            }

            const fileName = `${driver}.yml`;
            const filePath = joinPath(locatorDirPath, fileName);

            const key = element_name;
            const yamlContent = `${key}:\n  region:\n    left_top_x: ${left_top_x}\n    left_top_y: ${left_top_y}\n    right_down_x: ${right_down_x}\n    right_down_y: ${right_down_y}\n`;

            let finalContent = yamlContent;

            if (await this.fileService.exists(filePath)) {
                const existingContent = (await this.fileService.readFile(filePath)).value.toString();
                if (!existingContent.includes(key + ':')) {
                    finalContent = existingContent.trim() === '' ? yamlContent : existingContent + '\n' + yamlContent;
                } else {
                    this.logService.info(`区域 ${key} 已存在于 ${fileName} 中，将更新坐标值`);
                    const lines = existingContent.split('\n');
                    let inKeySection = false;
                    let inRegionSection = false;
                    let updated = false;

                    for (let i = 0; i < lines.length; i++) {
                        const trimmedLine = lines[i].trim();
                        if (trimmedLine === `${key}:`) {
                            inKeySection = true;
                            inRegionSection = false; // Reset when entering a new key section
                        } else if (inKeySection && trimmedLine === 'region:') {
                            inRegionSection = true;
                        } else if (inKeySection && inRegionSection) {
                            if (trimmedLine.startsWith('left_top_x:')) {
                                lines[i] = `    left_top_x: ${left_top_x}`;
                            } else if (trimmedLine.startsWith('left_top_y:')) {
                                lines[i] = `    left_top_y: ${left_top_y}`;
                            } else if (trimmedLine.startsWith('right_down_x:')) {
                                lines[i] = `    right_down_x: ${right_down_x}`;
                            } else if (trimmedLine.startsWith('right_down_y:')) {
                                lines[i] = `    right_down_y: ${right_down_y}`;
                                updated = true;
                                // Assuming all region coordinates are together, break after updating the last one
                                break;
                            }
                        } else if (inKeySection && trimmedLine && !trimmedLine.startsWith(' ')) {
                            // Found next top-level key, meaning current key's section is over
                            if (inRegionSection) { // If we were in region section but didn't update, something is wrong or structure is unexpected
                                updated = false; // Mark as not updated to append if necessary
                                break;
                            }
                            inKeySection = false;
                            inRegionSection = false;
                        }
                    }

                    if (updated) {
                        finalContent = lines.join('\n');
                    } else {
                        // If not updated (e.g., key existed but no 'region:' or coords, or structure was different)
                        // Append new content to be safe, or handle more complex merge/replace logic if needed.
                        // For now, let's assume if not updated cleanly, we append.
                        // A more robust solution might involve parsing YAML properly.
                        this.logService.warn(`区域 ${key} 在 ${fileName} 中结构不符合预期或未找到更新点，将尝试追加。`);
                        finalContent = existingContent + '\n' + yamlContent;
                    }
                }
            } else {
                this.logService.info(`创建新文件: ${filePath.toString()}`);
            }

            await this.fileService.writeFile(filePath, VSBuffer.fromString(finalContent));
            this.notificationService.info(localize('regionLocatorSaved', "已保存区域 '{0}' 到 {1}", key, fileName));
        } catch (err) {
            this.logService.error(`保存REGION到locator目录失败: ${err}`);
            if (err instanceof Error) {
                this.notificationService.error(localize('regionLocatorSaveFailed', "保存区域信息失败: {0}", err.message));
            }
            throw err;
        }
    }

    /**
     * 将对象格式化为无引号的JSON风格字符串，匹配示例文件格式
     * @param obj 要格式化的对象
     * @param indent 缩进级别
     * @returns JSON风格的字符串，无引号
     */
    private formatObjectAsJsonStyle(obj: any, indent: number = 0): string {
        if (!obj || typeof obj !== 'object' || Object.keys(obj).length === 0) {
            return '{}';
        }

        const indentStr = ' '.repeat(indent);
        let result = '{\n';
        const keys = Object.keys(obj);

        for (let i = 0; i < keys.length; i++) {
            const key = keys[i];
            const value = obj[key];
            const isLast = i === keys.length - 1;

            if (value === null || value === undefined) {
                result += `${indentStr}${key}: null${isLast ? '' : ','}\n`;
            } else if (typeof value === 'object' && !Array.isArray(value)) {
                const nestedObj = this.formatObjectAsJsonStyle(value, indent + 2);
                result += `${indentStr}${key}: ${nestedObj}${isLast ? '' : ','}\n`;
            } else if (Array.isArray(value)) {
                if (value.length === 0) {
                    result += `${indentStr}${key}: []${isLast ? '' : ','}\n`;
                } else {
                    result += `${indentStr}${key}: [\n`;
                    for (let j = 0; j < value.length; j++) {
                        const item = value[j];
                        const isLastItem = j === value.length - 1;
                        if (typeof item === 'object') {
                            const nestedObj = this.formatObjectAsJsonStyle(item, indent + 4);
                            result += `${indentStr}  ${nestedObj}${isLastItem ? '' : ','}\n`;
                        } else if (typeof item === 'string') {
                            result += `${indentStr}  "${item}"${isLastItem ? '' : ','}\n`;
                        } else {
                            result += `${indentStr}  ${item}${isLastItem ? '' : ','}\n`;
                        }
                    }
                    result += `${indentStr}]${isLast ? '' : ','}\n`;
                }
            } else if (typeof value === 'string') {
                result += `${indentStr}${key}: "${value}"${isLast ? '' : ','}\n`;
            } else {
                // 数字、布尔等其他类型
                result += `${indentStr}${key}: ${value}${isLast ? '' : ','}\n`;
            }
        }

        result += ' '.repeat(indent - 2) + '}';
        return result;
    }

    private restoreMethodWindow(methodWindow: Window, windowX: number, windowY: number, windowW: number, windowH: number, status: string): void {
        if (methodWindow && !methodWindow.closed) {
            try {
                this.logService.info(`恢复方法窗口: ${status}`);
                methodWindow.resizeTo(windowW, windowH);
                methodWindow.moveTo(windowX, windowY);
                methodWindow.focus();
            } catch (err) {
                this.logService.error(`恢复方法窗口失败: ${err}`);
            }
        }
    }

    /**
     * 创建快捷键输入对话框
     */
    private createHotkeyInputDialog(methodWindow: Window, doc: Document, paramName: string): void {
        try {
            // 创建模态遮罩
            const modal = doc.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0,0,0,0.5);
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 10000;
            `;

            // 创建对话框
            const dialog = doc.createElement('div');
            dialog.style.cssText = `
                background: #fff;
                padding: 20px;
                border-radius: 8px;
                max-width: 400px;
                min-width: 350px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            `;

            // 标题
            const title = doc.createElement('h3');
            title.textContent = '添加快捷键';
            title.style.cssText = 'margin: 0 0 16px 0; color: #333;';

            // 快捷键名称输入
            const nameLabel = doc.createElement('label');
            nameLabel.textContent = '快捷键名称:';
            nameLabel.style.cssText = 'display: block; margin-bottom: 8px; color: #555;';

            const nameInput = doc.createElement('input');
            nameInput.type = 'text';
            nameInput.placeholder = '例如: 编辑';
            nameInput.style.cssText = `
                width: 100%;
                padding: 8px;
                margin-bottom: 16px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            `;

            // 快捷键字符串输入
            const keyLabel = doc.createElement('label');
            keyLabel.textContent = '快捷键组合:';
            keyLabel.style.cssText = 'display: block; margin-bottom: 8px; color: #555;';

            const keyInput = doc.createElement('input');
            keyInput.type = 'text';
            keyInput.placeholder = '例如: alt+e 或 ctrl+c';
            keyInput.style.cssText = `
                width: 100%;
                padding: 8px;
                margin-bottom: 20px;
                border: 1px solid #ddd;
                border-radius: 4px;
                box-sizing: border-box;
            `;

            // 按钮容器
            const buttonContainer = doc.createElement('div');
            buttonContainer.style.cssText = 'display: flex; justify-content: flex-end; gap: 12px;';

            // 取消按钮
            const cancelBtn = doc.createElement('button');
            cancelBtn.textContent = '取消';
            cancelBtn.style.cssText = `
                padding: 8px 16px;
                border: 1px solid #ddd;
                background: #f5f5f5;
                border-radius: 4px;
                cursor: pointer;
            `;

            // 确认按钮
            const confirmBtn = doc.createElement('button');
            confirmBtn.textContent = '确认';
            confirmBtn.style.cssText = `
                padding: 8px 16px;
                border: none;
                background: #007acc;
                color: white;
                border-radius: 4px;
                cursor: pointer;
            `;

            // 组装对话框
            dialog.appendChild(title);
            dialog.appendChild(nameLabel);
            dialog.appendChild(nameInput);
            dialog.appendChild(keyLabel);
            dialog.appendChild(keyInput);
            buttonContainer.appendChild(cancelBtn);
            buttonContainer.appendChild(confirmBtn);
            dialog.appendChild(buttonContainer);
            modal.appendChild(dialog);
            doc.body.appendChild(modal);

            // 聚焦第一个输入框
            nameInput.focus();

            // 事件处理
            const cleanup = () => {
                if (modal.parentNode) {
                    modal.parentNode.removeChild(modal);
                }
            };

            // 取消按钮事件
            cancelBtn.addEventListener('click', () => {
                cleanup();
                this.dispatcher.dispatchControl('快捷键输入已取消');
            });

            // 确认按钮事件
            confirmBtn.addEventListener('click', async () => {
                const name = nameInput.value.trim();
                const key = keyInput.value.trim();

                if (!name || !key) {
                    alert('请填写完整的快捷键信息');
                    return;
                }

                try {
                    // 获取当前driver值
                    const driverInput = doc.querySelector('#param-driver') as HTMLSelectElement;
                    const driver = driverInput?.value || 'default';

                    // 保存快捷键到locator文件
                    await this.saveHotkeyLocator(name, key, driver);

                    // 更新输入框
                    const keyInputField = doc.querySelector(`#param-${paramName}`) as HTMLInputElement;
                    if (keyInputField) {
                        keyInputField.value = name;
                    }

                    const datamapInput = doc.querySelector(`#param-${paramName}-datamap`) as HTMLInputElement;
                    if (datamapInput) {
                        datamapInput.value = JSON.stringify({ name, key });
                    }

                    this.notificationService.info(localize('hotkeyAdded', '已添加快捷键: {0} ({1})', name, key));
                    this.dispatcher.dispatchControl(`已添加快捷键: ${name} (${key})`);

                    cleanup();
                } catch (error) {
                    this.logService.error(`保存快捷键失败: ${error}`);
                    this.notificationService.error(localize('saveHotkeyFailed', '保存快捷键失败: {0}', error));
                }
            });

            // ESC键取消
            const keyHandler = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                    cleanup();
                    this.dispatcher.dispatchControl('快捷键输入已取消');
                    doc.removeEventListener('keydown', keyHandler);
                }
            };
            doc.addEventListener('keydown', keyHandler);

        } catch (error) {
            this.logService.error(`创建快捷键输入对话框失败: ${error}`);
            this.notificationService.error(localize('createHotkeyDialogFailed', '创建快捷键输入对话框失败: {0}', error));
        }
    }

    /**
     * 保存快捷键到locator文件
     */
    public async saveHotkeyLocator(name: string, key: string, driver: string): Promise<void> {
        try {
            // 获取测试用例目录配置
            const testCasePathFromConfig = this.configurationService.getValue<string>('gat.testcasePath');
            if (!testCasePathFromConfig) {
                throw new Error('未配置 gat.testcasePath');
            }

            // 确定locator目录路径（与testcase同级）
            const locatorDirPath = joinPath(URI.file(testCasePathFromConfig), '..', 'locator');
            this.logService.info(`locator目录路径: ${locatorDirPath.toString()}`);

            // 确保locator目录存在
            try {
                await this.fileService.createFolder(locatorDirPath);
                this.logService.info('已确保locator目录存在');
            } catch (error) {
                if (error instanceof Error && error.name !== 'FileExistsError') {
                    throw error;
                }
            }

            // 确定文件路径
            const yamlFilePath = joinPath(locatorDirPath, `${driver}.yml`);
            this.logService.info(`快捷键locator文件路径: ${yamlFilePath.toString()}`);

            // 读取现有内容或创建新内容
            let yamlData: any = {};
            try {
                const file = await this.fileService.readFile(yamlFilePath);
                const existingContent = file.value.toString();
                if (existingContent.trim()) {
                    // 使用现有的YAML解析函数
                    yamlData = this.parseYamlContent(existingContent);
                }
            } catch (error) {
                this.logService.info('locator文件不存在，将创建新文件');
            }

            // 确保"快捷键"节点存在
            if (!yamlData['快捷键']) {
                yamlData['快捷键'] = {};
            }

            // 添加新的快捷键
            yamlData['快捷键'][name] = key;

            // 转换为YAML格式并写入文件
            const yamlContent = this.convertToYamlFormat(yamlData);
            await this.fileService.writeFile(yamlFilePath, VSBuffer.fromString(yamlContent));

            this.logService.info(`快捷键已保存到locator文件: ${name} = ${key}`);

        } catch (error) {
            this.logService.error(`保存快捷键到locator文件失败: ${error}`);
            throw error;
        }
    }

    /**
     * 简单的YAML解析函数
     */
    private parseYamlContent(content: string): any {
        const result: any = {};
        const lines = content.split('\n');
        let currentPath: string[] = [];

        for (const line of lines) {
            const trimmed = line.trim();
            if (!trimmed || trimmed.startsWith('#')) continue;

            const indent = line.length - line.trimStart().length;
            const indentLevel = Math.floor(indent / 2);

            if (line.includes(':')) {
                const [key, value] = line.split(':', 2);
                const cleanKey = key.trim();
                const cleanValue = value ? value.trim() : '';

                // 调整路径到当前缩进级别
                currentPath = currentPath.slice(0, indentLevel);
                currentPath.push(cleanKey);

                // 设置值
                let target = result;
                for (let i = 0; i < currentPath.length - 1; i++) {
                    if (!target[currentPath[i]]) {
                        target[currentPath[i]] = {};
                    }
                    target = target[currentPath[i]];
                }

                if (cleanValue) {
                    target[cleanKey] = cleanValue;
                } else {
                    target[cleanKey] = {};
                }
            }
        }

        return result;
    }

    /**
     * 转换对象为YAML格式
     */
    private convertToYamlFormat(obj: any, indent: number = 0): string {
        let result = '';
        const indentStr = ' '.repeat(indent);

        for (const [key, value] of Object.entries(obj)) {
            if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
                result += `${indentStr}${key}:\n`;
                result += this.convertToYamlFormat(value, indent + 2);
            } else {
                result += `${indentStr}${key}: ${value}\n`;
            }
        }

        return result;
    }
}
