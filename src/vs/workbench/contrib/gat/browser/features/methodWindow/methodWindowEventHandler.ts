/*---------------------------------------------------------------------------------------------
 *  Copyright (c) 2025 kylin robot. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { Disposable } from '../../../../../../base/common/lifecycle.js';
import { Emitter } from '../../../../../../base/common/event.js';
import { ILogService } from '../../../../../../platform/log/common/log.js';
import { INotificationService } from '../../../../../../platform/notification/common/notification.js';
import { localize } from '../../../../../../nls.js';
import { MethodDataLoader } from './methodDataLoader.js';
import { WidgetCaptureManager } from './widgetCapture/widgetCaptureManager.js';
import { IWidgetInfo } from './widgetCapture/types.js';
import { IWidgetCaptureService } from '../../../../../../platform/gat/common/widgetCaptureService.js';
import { GatMessageService } from '../messaging/gatMessageService.js';
import { INativeHostService } from '../../../../../../platform/native/common/native.js';
import { IFileService } from '../../../../../../platform/files/common/files.js';
import { INativeEnvironmentService } from '../../../../../../platform/environment/common/environment.js';
import { MessageDispatcher } from './messageDispatcher.js';
import { CaptureKeyElementHandler } from './captureKeyElementHandler.js';
import { IScreenshotCaptureResult } from './widgetCapture/screenshotCaptureHandler.js';
import { IRegionCaptureResult } from './widgetCapture/regionCaptureService.js'; // 新增导入
import { IConfigurationService } from '../../../../../../platform/configuration/common/configuration.js';
import { URI } from '../../../../../../base/common/uri.js';
import { joinPath } from '../../../../../../base/common/resources.js';
import { IWebviewService } from '../../../../../../workbench/contrib/webview/browser/webview.js';
import { IWorkspaceContextService } from '../../../../../../platform/workspace/common/workspace.js';
// 使用 js-yaml 解析 locator YAML
import { parse as robustYamlParse } from '../lib/yamlUtils.js';

/**
 * 负责处理窗口中的各种事件
 */
// 为window添加electronAPI类型定义
declare global {
    interface Window {
        electronAPI?: {
            ipcRenderer: {
                send: (channel: string, ...args: any[]) => void;
                on: (channel: string, listener: (event: any, ...args: any[]) => void) => void;
            };
        };
    }
}

export class MethodWindowEventHandler extends Disposable {
    private selectedMethod: string | null = null;
    private parameterValues: Record<string, any> = {};
    private methodWindow: Window | null = null;
    private readonly widgetCaptureManager: WidgetCaptureManager;
    private readonly messageService: GatMessageService;
    private readonly dispatcher: MessageDispatcher;
    private readonly captureHandler: CaptureKeyElementHandler;
    private readonly disposables = new Set<{ dispose: () => void }>();

    // 全局存储已插入的return_var值，供object类型参数选择
    private static insertedReturnVars: Array<{ name: string, description?: string }> = [];

    // 添加事件发射器，用于发送消息到录制工具条
    private readonly _onSendMessage = new Emitter<{ type: string, message: string }>();
    public readonly onSendMessage = this._onSendMessage.event;

    // 添加捕获控件消息事件发射器
    private readonly _onCaptureControl = new Emitter<string>();
    public readonly onCaptureControl = this._onCaptureControl.event;

    // 测试用例上下文
    private testCaseContext: any = {};

    // 应用列表，用于应用对象下拉框
    private appList: Array<{ label: string, value: string, en?: string, selected?: boolean }> = [];

    public setTestCaseContext(ctx: any): void { this.testCaseContext = ctx; }
    public setAppList(apps: Array<{ label: string, value: string, en?: string, selected?: boolean }>): void { this.appList = apps; }

    /**
     * 获取应用对象列表
     */
    public getAppList(): Array<{ label: string, value: string, en?: string, selected?: boolean }> {
        return this.appList;
    }

    /**
     * 检查是否要增加return_x
     */
    private checkAddReturnVar(doc: Document, params: any, description: string = '') {
        if (!params) {
            return;
        }
        Object.keys(params).forEach((key) => {
            const name = params[key];
            if (typeof name === 'string' && name.trim() && key.startsWith('return_')) {
                let desc = description;
                if (!desc) {
                    const element = doc.querySelector(`#param-${key}`);
                    if (element) {
                        desc = (element as HTMLElement)?.dataset?.desc || key;
                    }
                }

                this.logService.info(`已添加return_var ${name} (${desc}) 到全局存储`);
                MethodWindowEventHandler.addReturnVar(name, desc);
            }
        });
    }

    /**
     * 添加return_var到全局存储
     */
    public static addReturnVar(name: string, description: string = ''): void {
        if (!name || !name.trim()) {
            return;
        }

        const duplex = MethodWindowEventHandler.insertedReturnVars.find((item) => item.name === name);
        if (duplex) {
            return;
        }
        MethodWindowEventHandler.insertedReturnVars.push({ name, description });
    }

    /**
     * 获取所有已插入的return_var值
     */
    public static getInsertedReturnVars(): any[] {
        return MethodWindowEventHandler.insertedReturnVars || [];
    }

    /**
     * 清空所有return_var（用于重置）
     */
    public static clearReturnVars(): void {
        MethodWindowEventHandler.insertedReturnVars.length = 0;
    }

    constructor(
        private readonly methodDataLoader: MethodDataLoader,
        @ILogService private readonly logService: ILogService,
        @INotificationService private readonly notificationService: INotificationService,
        @IWidgetCaptureService widgetCaptureService: IWidgetCaptureService,
        @INativeHostService nativeHostService: INativeHostService,
        @IFileService private readonly fileService: IFileService,
        @INativeEnvironmentService private readonly environmentService: INativeEnvironmentService,
        @IConfigurationService private readonly configurationService: IConfigurationService,
        @IWebviewService private readonly webviewService: IWebviewService,
        @IWorkspaceContextService private readonly workspaceContextService: IWorkspaceContextService
    ) {
        super();

        this.logService.info('MethodWindowEventHandler 构造函数开始初始化');

        // 初始化消息服务
        this.messageService = GatMessageService.getInstance(this.logService, 'MethodWindowEventHandler');

        // 初始化消息分发器
        this.dispatcher = new MessageDispatcher(
            this.logService,
            this.messageService,
            this._onCaptureControl
        );

        // 初始化控件捕获管理器
        this.widgetCaptureManager = new WidgetCaptureManager(
            this.logService,
            this.notificationService,
            widgetCaptureService,
            nativeHostService,
            this.fileService,
            this.environmentService,
            this.webviewService
        );

        // 初始化捕获处理器
        this.captureHandler = new CaptureKeyElementHandler(
            this.dispatcher,
            this.widgetCaptureManager,
            this.notificationService,
            this.logService,
            this.fileService,
            this.configurationService,
            this.workspaceContextService
        );

        // 设置控件捕获结果监听器
        this.setupControlModalResultListener();

        // 订阅截图完成事件，确保插入窗口接收到无论何种触发的截图结果
        this.widgetCaptureManager.onScreenshotCaptured((result: IScreenshotCaptureResult) => {
            this.logService.info(`收到截图完成事件: ${JSON.stringify(result)}`);
            const controlName = result.element_name ?? '';
            this.dispatcher.dispatchControl(localize('widgetCapturedIcon', "已捕获截图: {0}", controlName));
        });

        this.logService.info('MethodWindowEventHandler 构造函数初始化完成');
    }

    /**
     * 设置控制模态窗口确认结果监听器
     */
    private setupControlModalResultListener(): void {
        try {
            // 尝试通过Electron IPC监听
            if (window.electronAPI && window.electronAPI.ipcRenderer) {
                this.logService.info('设置控制模态确认结果的IPC监听器');
                window.electronAPI.ipcRenderer.on('control-modal-result', (_event: any, result: { confirmed: boolean, data: any }) => {
                    this.logService.info(`通过IPC收到控制模态确认结果: ${JSON.stringify(result)}`);
                    this.dispatcher.handleControlModalResult(result);
                });
                return;
            }

            // 备用：设置全局事件监听器
            window.addEventListener('message', (event) => {
                if (event.data && event.data.type === 'control-modal-result') {
                    this.logService.info(`通过postMessage收到控制模态确认结果: ${JSON.stringify(event.data.result)}`);
                    this.dispatcher.handleControlModalResult(event.data.result);
                }
            });

            // 增加对localStorage的监听
            let lastCheckedTimestamp = Date.now();
            const checkLocalStorage = () => {
                try {
                    const resultStr = localStorage.getItem('control-modal-result');
                    if (resultStr) {
                        const resultData = JSON.parse(resultStr);
                        // 确保是新的消息
                        if (resultData.timestamp > lastCheckedTimestamp) {
                            this.logService.info(`通过localStorage收到控制模态确认结果: ${resultStr}`);
                            lastCheckedTimestamp = resultData.timestamp;
                            this.dispatcher.handleControlModalResult(resultData);
                            // 清除处理过的消息
                            localStorage.removeItem('control-modal-result');
                        }
                    }
                } catch (e) {
                    // 忽略错误，继续轮询
                }
            };

            // 定期检查localStorage
            const intervalId = setInterval(checkLocalStorage, 500);

            // 需要在销毁时清除interval
            this.disposables.add({ dispose: () => clearInterval(intervalId) });

            this.logService.info('已设置控制模态窗口确认结果监听器');
        } catch (error) {
            this.logService.error(`设置控制模态窗口确认结果监听器失败: ${error}`);
        }
    }

    /**
     * 获取当前选中的方法
     */
    public getSelectedMethod(): string | null {
        return this.selectedMethod;
    }

    /**
     * 设置当前选中的方法
     */
    public setSelectedMethod(methodName: string | null): void {
        this.selectedMethod = methodName;
        // 重置参数值
        this.parameterValues = {};
    }

    /**
     * 设置方法窗口引用
     */
    public setMethodWindow(window: Window | null): void {
        this.methodWindow = window;

        // 将录制工具条窗口引用传递给方法窗口
        try {
            if (this.methodWindow && (globalThis as any).recordingToolbarWindow) {
                (this.methodWindow as any).recordingToolbarWindow = (globalThis as any).recordingToolbarWindow;
                this.logService.info('已将录制工具条窗口引用传递给方法窗口');
            } else {
                this.logService.warn('无法获取录制工具条窗口引用');
            }
        } catch (error) {
            this.logService.error(`传递录制工具条窗口引用时出错: ${error}`);
        }
    }

    /**
     * 设置事件监听器
     */
    public setupEventListeners(doc: Document, onMethodInserted: (methodName: string, parameters: Record<string, any>) => void, onClose: () => void): void {
        // 获取DOM元素
        const searchInput = doc.querySelector('.search-input') as HTMLInputElement;
        const methodItems = doc.querySelectorAll('.method-item');
        const categoryHeaders = doc.querySelectorAll('.category-header');
        const insertButton = doc.querySelector('.insert-button') as HTMLButtonElement;
        const closeButton = doc.querySelector('.close-button') as HTMLButtonElement;

        this.logService.info(`setupEventListeners: searchInput.disabled=${searchInput?.disabled}, methodItems=${methodItems.length}`);

        // 添加类别折叠/展开功能
        categoryHeaders.forEach(header => {
            header.addEventListener('click', () => {
                header.classList.toggle('collapsed');
                const items = header.nextElementSibling as HTMLElement;
                items.classList.toggle('collapsed');
            });
        });

        // 添加方法项点击事件
        methodItems.forEach(item => {
            item.addEventListener('click', () => {
                // 移除之前选中项的选中状态
                methodItems.forEach(i => i.classList.remove('selected'));

                // 添加选中状态
                item.classList.add('selected');
                const el = item as HTMLElement;
                // 优先使用 data-method，回退到文本
                const methodName = el.getAttribute('data-method') || el.textContent?.trim() || '';
                this.logService.info(`点击左侧方法项, methodName=${methodName}`);

                if (methodName) {
                    // 设置当前选中的方法
                    this.setSelectedMethod(methodName);

                    // 显示方法详情和参数
                    this.updateMethodDetails(doc, methodName);

                    // 设置参数表单事件监听器
                    this.setupParameterFormListeners(doc);
                }
            });
        });

        // 添加搜索功能
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                const searchText = searchInput.value.toLowerCase();

                // 过滤方法项
                methodItems.forEach(item => {
                    const methodName = (item as HTMLElement).getAttribute('data-method') || '';
                    const methodText = item.textContent?.toLowerCase() || '';

                    if (methodName.toLowerCase().includes(searchText) || methodText.includes(searchText)) {
                        (item as HTMLElement).style.display = '';
                    } else {
                        (item as HTMLElement).style.display = 'none';
                    }
                });

                // 更新类别显示状态
                categoryHeaders.forEach(header => {
                    const nextContainer = header.nextElementSibling as HTMLElement;
                    const hasVisibleItems = Array.from(nextContainer.querySelectorAll('.method-item'))
                        .some(item => (item as HTMLElement).style.display !== 'none');

                    (header as HTMLElement).style.display = hasVisibleItems ? '' : 'none';
                    nextContainer.style.display = hasVisibleItems ? '' : 'none';
                });
            });
        }

        // 添加插入按钮点击事件
        if (insertButton) {
            insertButton.addEventListener('click', () => {
                if (!this.selectedMethod) {
                    return;
                }
                // 验证并收集参数
                if (!this.validateRequiredParameters(doc)) {
                    return;
                }
                this.updateParameterValues(doc);
                const params = this.getParameterValues();

                // 查找所有datamap字段，分别处理UNI和ICON
                const datamapKeys = Object.keys(params).filter(key =>
                    key.endsWith('-datamap') && typeof params[key] === 'string'
                );
                if (datamapKeys.length > 0) {
                    this.logService.info(`找到${datamapKeys.length}个datamap字段`);
                    const driverElem = doc.querySelector('#param-driver') as HTMLInputElement | HTMLSelectElement;
                    const driver = driverElem?.value;
                    if (driver) {
                        this.logService.info(`准备使用driver: ${driver}`);
                        for (const datamapKey of datamapKeys) {
                            const raw = params[datamapKey] as string;
                            const paramName = datamapKey.replace(/-datamap$/, '');
                            const select = doc.querySelector(`#param-${paramName}-type`) as HTMLSelectElement;
                            const keyType = select?.value;
                            if (keyType === 'UNI') {
                                try {
                                    const widgetInfo = JSON.parse(raw);
                                    this.logService.info(`解析UNI控件信息成功: ${raw.substring(0, 100)}...`);
                                    this.captureHandler.saveUniLocator(widgetInfo, driver).catch(err => {
                                        this.logService.error(`保存UNI控件失败: ${err}`);
                                    });
                                } catch (err) {
                                    this.logService.error(`解析UNI控件信息失败: ${err}`);
                                }
                            } else if (keyType === 'ICON' || keyType === 'IMAGE') {
                                const alias = raw;
                                if (alias) {
                                    this.logService.info(`处理ICON截取alias: ${alias}`);
                                    this.captureHandler.saveIconLocator(alias, driver, this.testCaseContext.name).catch(err => {
                                        this.logService.error(`保存ICON截图失败: ${err}`);
                                    });
                                }
                            } else if (keyType === 'POSITION') {
                                let positionInfoToSave: any = null;
                                const currentKey = datamapKey;

                                try {
                                    if (typeof raw === 'string') {
                                        if (raw.trim() === '') {
                                            this.logService.warn(`[MethodWindowEventHandler] Received empty string for POSITION raw data. Key: ${currentKey}. Skipping save.`);
                                        } else {
                                            positionInfoToSave = JSON.parse(raw);
                                            this.logService.info(`[MethodWindowEventHandler] Successfully parsed POSITION JSON string. Key: ${currentKey}. Data: ${JSON.stringify(positionInfoToSave)}`);
                                        }
                                    } else if (typeof raw === 'object' && raw !== null) {
                                        positionInfoToSave = raw; // Assume it's already the object
                                        this.logService.info(`[MethodWindowEventHandler] Received object for POSITION raw data. Key: ${currentKey}. Data: ${JSON.stringify(positionInfoToSave)}`);
                                    } else if (raw === null || raw === undefined) {
                                        this.logService.warn(`[MethodWindowEventHandler] Received null or undefined raw data for POSITION. Key: ${currentKey}. Skipping save.`);
                                    } else {
                                        this.logService.error(`[MethodWindowEventHandler] Invalid raw data type for POSITION: ${typeof raw}. Key: ${currentKey}. Value: ${String(raw)}. Skipping save.`);
                                    }

                                    if (positionInfoToSave) {
                                        // Basic validation for the structure
                                        if (typeof positionInfoToSave.name !== 'undefined' &&
                                            positionInfoToSave.coords && typeof positionInfoToSave.coords.x !== 'undefined' && typeof positionInfoToSave.coords.y !== 'undefined' &&
                                            typeof positionInfoToSave.type !== 'undefined') {

                                            this.logService.info(`[MethodWindowEventHandler] Processed POSITION info for save. Key: ${currentKey}. Data: ${JSON.stringify(positionInfoToSave)}`);
                                            this.captureHandler.savePositionLocator(positionInfoToSave, driver).catch(err => {
                                                this.logService.error(`[MethodWindowEventHandler] 保存POSITION位置信息失败. Key: ${currentKey}. Error: ${err}`);
                                            });
                                        } else {
                                            this.logService.error(`[MethodWindowEventHandler] Processed POSITION info is missing required fields (name, coords, type). Key: ${currentKey}. Data: ${JSON.stringify(positionInfoToSave)}. Skipping save.`);
                                        }
                                    } else {
                                        // This case might be redundant if previous checks handle all null/undefined/empty scenarios for positionInfoToSave
                                        this.logService.warn(`[MethodWindowEventHandler] positionInfoToSave is null or invalid after processing raw data. Key: ${currentKey}. Raw was: ${String(raw)}. Skipping save.`);
                                    }
                                } catch (err) {
                                    const rawContent = typeof raw === 'string' ? raw : `type ${typeof raw}, value ${String(raw)}`;
                                    this.logService.error(`[MethodWindowEventHandler] 处理POSITION位置信息时发生错误. Key: ${currentKey}. Error: ${err}. Raw data: ${rawContent}`);
                                }
                            } else if (keyType === 'REGION') {
                                try {
                                    const regionInfo = JSON.parse(raw) as IRegionCaptureResult;
                                    this.logService.info(`解析REGION区域信息: ${JSON.stringify(regionInfo)}`);
                                    if (!regionInfo.element_name) {
                                        this.logService.error('REGION信息中缺少element_name，无法保存');
                                        this.notificationService.error(localize('regionSaveError.missingName', '区域信息缺少名称，无法保存。'));
                                    } else {
                                        this.captureHandler.saveRegionLocator(regionInfo, driver).catch(err => {
                                            this.logService.error(`保存REGION区域信息失败: ${err}`);
                                        });
                                    }
                                } catch (err) {
                                    this.logService.error(`解析REGION区域信息失败: ${err}`);
                                }
                            }
                        }
                    } else {
                        this.logService.warn('未找到应用对象元素或值为空，无法保存控件信息');
                    }
                }

                // 检测并存储return_var参数
                if (this.selectedMethod) {
                    this.checkAddReturnVar(doc, params);
                }

                // 继续原有的方法插入逻辑
                onMethodInserted(this.selectedMethod, params);

                // 根据"连续插入"复选框决定是否关闭窗口
                const continuous = (doc.querySelector('#continuousInsert') as HTMLInputElement | null)?.checked;
                if (!continuous) {
                    onClose();
                }
            });
        }

        // 添加关闭按钮点击事件
        if (closeButton) {
            closeButton.addEventListener('click', () => {
                onClose();
            });
        }

        // 默认选中第一个方法，仅当没有编辑预选时
        const alreadySelected = doc.querySelector('.method-item.selected');
        if (!alreadySelected && methodItems.length > 0) {
            (methodItems[0] as HTMLElement).click();
        }
    }

    /**
     * 设置参数表单事件监听器
     */
    private setupParameterFormListeners(doc: Document): void {
        // 监听表单变化
        const formInputs = doc.querySelectorAll('.form-input, .form-select, .form-checkbox, .position-input, .form-radio, .key-type-select');
        formInputs.forEach(input => {
            input.addEventListener('change', () => {
                this.updateParameterValues(doc);
            });

            // 对于文本输入，也监听输入事件
            if (input instanceof HTMLInputElement && input.type === 'text') {
                input.addEventListener('input', () => {
                    this.updateParameterValues(doc);
                });
            }
        });

        // 监听捕获元素按钮点击
        const captureButtons = doc.querySelectorAll('.capture-button');
        captureButtons.forEach(button => {
            // 检查是否是key参数的捕获按钮
            const keyContainer = button.closest('.key-input-container');
            if (keyContainer) {
                // 已经在createKeyInputWithDropdown中添加了事件监听器
                // 这里不需要重复添加
            } else {
                // 对于位置参数的捕获按钮
                button.addEventListener('click', () => {
                    this.dispatcher.dispatchControl('进入UNI捕获状态，通过Ctrl+鼠标点击以选择控件');
                    this.logService.info('进入UNI捕获状态，通过Ctrl+鼠标点击以选择控件');

                    // 获取相关参数名
                    const positionContainer = button.closest('.position-container');
                    if (!positionContainer) {
                        return;
                    }

                    const formGroup = positionContainer.closest('.form-group');
                    if (!formGroup) {
                        return;
                    }

                    const paramName = formGroup.getAttribute('data-param-name');
                    if (!paramName) {
                        return;
                    }

                    // 隐藏窗口，以便用户可以点击要捕获的元素
                    if (this.methodWindow && !this.methodWindow.closed) {
                        // 保存窗口位置，以便稍后恢复
                        const windowX = this.methodWindow.screenX;
                        const windowY = this.methodWindow.screenY;

                        // 最小化窗口
                        this.logService.info('将窗口设置为失去焦点状态');
                        this.methodWindow.blur();

                        // 启动UNI捕获
                        this.widgetCaptureManager.startCapture('UNI', (widgetInfo: IWidgetInfo) => {
                            // 捕获完成后恢复窗口
                            if (this.methodWindow && !this.methodWindow.closed) {
                                this.methodWindow.moveTo(windowX, windowY);
                                this.methodWindow.focus();

                                // 获取坐标信息
                                const coords = widgetInfo.coords || { x: 0, y: 0 };

                                // 更新位置输入
                                const xInput = doc.querySelector(`[name="${paramName}-x"]`) as HTMLInputElement;
                                const yInput = doc.querySelector(`[name="${paramName}-y"]`) as HTMLInputElement;

                                if (xInput && yInput) {
                                    xInput.value = coords.x.toString();
                                    yInput.value = coords.y.toString();
                                    this.updateParameterValues(doc);

                                    this.notificationService.info(localize('elementCaptured', "已捕获元素，坐标: ({0}, {1})", coords.x, coords.y));

                                    // 向录制工具条发送捕获成功消息
                                    this.dispatcher.dispatchControl(`已捕获元素，坐标: (${coords.x}, ${coords.y})`);
                                }
                            }
                        });
                    }
                });
            }
        });
    }

    /**
     * 更新方法详情
     */
    public updateMethodDetails(doc: Document, methodName: string | null): void {
        if (!methodName) {
            return;
        }

        const contentPlaceholder = doc.querySelector('.content-placeholder') as HTMLElement;
        const methodDetails = doc.querySelector('.method-details') as HTMLElement;
        const methodTitle = doc.querySelector('.method-title') as HTMLElement;
        const descriptionText = doc.querySelector('.description-box span:last-child') as HTMLElement;
        const examplesContent = doc.querySelector('.examples-content') as HTMLElement;

        // 获取方法详情
        const methodInfo = this.methodDataLoader.getMethodDetail(methodName);
        if (!methodInfo) {
            if (contentPlaceholder) contentPlaceholder.style.display = 'flex';
            if (methodDetails) methodDetails.style.display = 'none';
            return;
        }

        // 隐藏占位内容，显示方法详情
        if (contentPlaceholder) contentPlaceholder.style.display = 'none';
        if (methodDetails) methodDetails.style.display = 'block';

        // 更新方法标题
        if (methodTitle) {
            // 显示方法的真实名称，后面括号中显示中文名称
            methodTitle.textContent = methodName;
            if (methodInfo.overview) {
                methodTitle.textContent += ` (${methodInfo.overview})`;
            }
        }

        // 更新方法描述
        if (descriptionText) {
            const description = methodInfo.description || methodInfo.overview;
            if (description) {
                descriptionText.textContent = '描述：' + description;
            } else {
                descriptionText.textContent = '无描述';
            }
        }

        // 更新参数表单
        this.logService.info('方法参数信息:', methodInfo.parameters);
        this.updateParametersForm(doc, methodInfo.parameters || []);

        // 调试：打印 appList 和 TestCaseAppList
        this.logService.info('调试: appList=', JSON.stringify(this.appList));
        this.logService.info('调试: TestCaseAppList=', JSON.stringify(this.testCaseContext.TestCaseAppList));

        // 更新示例
        if (examplesContent) examplesContent.textContent = methodInfo.examples || '无示例';

        // 重置参数值，但如果方法有默认值，则使用默认值作为初始值
        this.parameterValues = {};

        // 新增：应用方法的默认值
        if (methodInfo.defaultValues && Object.keys(methodInfo.defaultValues).length > 0) {
            this.logService.info(`应用方法 ${methodName} 的默认值:`, methodInfo.defaultValues);
            // 将默认值复制到参数值中
            Object.assign(this.parameterValues, methodInfo.defaultValues);

            // 应用默认值到表单控件中
            setTimeout(() => {
                this.applyDefaultValuesToForm(doc, methodInfo.defaultValues);
            }, 100); // 延时确保表单控件已创建完成
        }

        // 新增：设置选中方法并高亮左侧列表项，滚动到可见
        this.setSelectedMethod(methodName);
        // 移除旧选中
        const prev = doc.querySelector('.method-item.selected') as HTMLElement | null;
        if (prev) {
            prev.classList.remove('selected');
        }
        // 高亮当前并滚动
        const items = doc.querySelectorAll('.method-item');
        this.logService.info(`左侧 method-item 数量: ${items.length}`);
        const itemNames = Array.from(items).map(i => (i as HTMLElement).getAttribute('data-method')).join(',');
        this.logService.info(`method-item 列表: ${itemNames}`);
        const current = doc.querySelector(`.method-item[data-method="${methodName}"]`) as HTMLElement | null;
        if (current) {
            this.logService.info(`找到 method-item: ${methodName}`);
            current.classList.add('selected');
            current.scrollIntoView({ block: 'nearest' });
            this.logService.info(`高亮左侧方法项: ${methodName}`);
        } else {
            this.logService.warn(`未找到左侧 method-item: ${methodName}`);
        }

        this.logService.info(`已更新方法详情: ${methodName}`);
    }

    /**
     * 更新参数表单
     */
    private updateParametersForm(doc: Document, parameters: any[]): void {
        // 打印原始参数，使用 INFO 级别以确保日志可见
        const rawParameters = parameters;
        this.logService.info('updateParametersForm rawParameters: ' + JSON.stringify(rawParameters));
        const paramsArray = Array.isArray(rawParameters) ? rawParameters : [];
        if (!Array.isArray(rawParameters)) {
            this.logService.error('updateParametersForm: parameters 不是数组，将使用空数组. 值: ' + JSON.stringify(rawParameters));
        }
        this.logService.info(`更新参数表单，参数数量: ${paramsArray.length}`);

        const parametersForm = doc.querySelector('.parameters-form') as HTMLFormElement;
        if (!parametersForm) {
            this.logService.error(`未找到参数表单元素`);
            return;
        }

        // 清空现有表单
        while (parametersForm.firstChild) {
            parametersForm.removeChild(parametersForm.firstChild);
        }

        // 如果没有参数，显示提示
        if (!paramsArray || paramsArray.length === 0) {
            const noParamsMsg = doc.createElement('div');
            noParamsMsg.className = 'no-params-message';
            noParamsMsg.textContent = '此方法没有参数';
            parametersForm.appendChild(noParamsMsg);
            return;
        }

        // 按参数类型分组
        const basicParams: any[] = [];
        const positionParams: any[] = [];
        const otherParams: any[] = [];
        const advanceParams: any[] = [];  // 高级参数

        paramsArray.forEach(param => {
            // 如果参数不显示，跳过
            if (param.show === false || param.show === 'False') {
                return;
            }

            // 分类参数
            if (param.name === 'driver') {
                // driver参数放在最前面
                basicParams.unshift(param);
            } else if (param.position && param.position.length > 0) {
                // 位置参数
                positionParams.push(param);
            } else if (param?.AdvancePramname) {
                advanceParams.push(param);
            } else {
                // 其他参数
                otherParams.push(param);
            }
        });

        this.logService.info(`更新参数表单，高级参数数量: ${advanceParams.length}, 值：${JSON.stringify(advanceParams)}`);

        // 按顺序创建参数表单项
        [...basicParams, ...positionParams, ...otherParams].forEach(param => {
            this.createParameterFormGroup(doc, parametersForm, param);
        });

        // 创建高级参数
        if (advanceParams.length > 0) {
            this.createAdvanceParams(doc, parametersForm, advanceParams);
        }
    }

    private createAdvanceParams(doc: Document, container: HTMLElement, advanceParams: any[]) {
        const advanceParamsContainer = doc.createElement('div');
        advanceParamsContainer.className = 'advance-params-container';

        const advanceParamsContent = doc.createElement('div');
        advanceParamsContent.className = 'advance-params-content';

        const advanceParamsHeader = doc.createElement('div');
        advanceParamsHeader.classList.add('advance-params-header');
        advanceParamsHeader.classList.add('collapsed');
        advanceParamsHeader.textContent = '高级选项';
        // 添加折叠/展开功能
        advanceParamsHeader.addEventListener('click', () => {
            const isCollapsed = advanceParamsContent.style.display === 'none';
            if (isCollapsed) {
                advanceParamsContent.style.display = 'block';
                advanceParamsHeader.classList.remove('collapsed');
            } else {
                advanceParamsContent.style.display = 'none';
                advanceParamsHeader.classList.add('collapsed');
            }
        });

        const returnParams: any[] = [];
        advanceParams.forEach(param => {
            if (param?.name && param.name.startsWith('return_')) {
                returnParams.push(param);
            } else {
                this.createParameterFormGroup(doc, advanceParamsContent, param);
            }
        });

        // 生成的变量
        if (returnParams.length > 0) {
            // 生成的变量
            const returnParamsContainer = doc.createElement('div');
            returnParamsContainer.className = 'advance-params-container';

            const returnParamsContent = doc.createElement('div');
            returnParamsContent.className = 'advance-params-content';

            const returnParamsHeader = doc.createElement('div');
            returnParamsHeader.classList.add('advance-params-header');
            returnParamsHeader.classList.add('collapsed');
            returnParamsHeader.textContent = '生成的变量';
            // 添加折叠/展开功能
            returnParamsHeader.addEventListener('click', () => {
                const isCollapsed = returnParamsContent.style.display === 'none';
                if (isCollapsed) {
                    returnParamsContent.style.display = 'block';
                    returnParamsHeader.classList.remove('collapsed');
                } else {
                    returnParamsContent.style.display = 'none';
                    returnParamsHeader.classList.add('collapsed');
                }
            });

            returnParams.forEach(param => {
                this.createReturnParameterFormGroup(doc, returnParamsContent, param);
            });

            returnParamsContent.style.display = 'none';
            returnParamsContainer.appendChild(returnParamsHeader);
            returnParamsContainer.appendChild(returnParamsContent);
            advanceParamsContent.appendChild(returnParamsContainer);
        }

        advanceParamsContent.style.display = 'none';

        advanceParamsContainer.appendChild(advanceParamsHeader);
        advanceParamsContainer.appendChild(advanceParamsContent);
        container.appendChild(advanceParamsContainer);
    }

    /**附加组件显示条件 */
    private appendCondictions(element: HTMLElement, param: any) {
        if (param?.condiction) {
            let selected = undefined;
            if (Array.isArray(param.condiction)) {
                const selectedCondictions = param.condiction.filter((item: any) => item?.selected);
                if (selectedCondictions.length > 0) {
                    selected = selectedCondictions[0]?.selected;
                }
            } else {
                selected = param.condiction?.selected;
            }

            if (selected) {
                element.dataset.condiction_selected = selected;
                this.logService.info(`组件 ${param.name} 只在 ${selected} 时展示`);
            }
        }
    }

    /** 创建生成的变量表单项 */
    private createReturnParameterFormGroup(doc: Document, container: HTMLElement, param: any): void {
        this.logService.info(`创建生成的变量表单组: name=${param.name}, paramname=${param.paramname}, type=${param.type}`);
        // 创建表单项容器
        const formGroup = doc.createElement('div');
        formGroup.className = 'form-group';
        formGroup.dataset.paramName = param.name;
        this.appendCondictions(formGroup, param);

        // 创建switch开关
        const switchContainer = document.createElement('div');
        switchContainer.className = 'switch-button-container';

        const switchLabel = document.createElement('label');
        switchLabel.className = 'switch-button';

        const switchInput = document.createElement('input');
        switchInput.className = 'switch-button-checkbox';
        switchInput.type = 'checkbox';
        switchInput.checked = true;

        const slider = document.createElement('span');
        slider.className = 'slider';

        switchLabel.appendChild(switchInput);
        switchLabel.appendChild(slider);
        switchContainer.appendChild(switchLabel);

        formGroup.appendChild(switchContainer);

        // 创建标签
        const label = doc.createElement('label');
        label.htmlFor = `param-${param.name}`;
        label.className = 'param-label';
        label.textContent = param.AdvancePramname || param.paramname || param.name;

        // 如果有描述，添加提示图标
        if (param.description) {
            const tooltip = doc.createElement('span');
            tooltip.className = 'tooltip-icon';
            tooltip.textContent = 'ⓘ';
            tooltip.title = param.description;
            label.appendChild(tooltip);
        }

        formGroup.appendChild(label);

        // 根据参数类型创建不同的输入控件
        const inputElement: HTMLElement = this.createStringInput(doc, param);
        inputElement.dataset.desc = param?.description || param?.paramname || param?.AdvancePramname;
        inputElement.dataset.disabled = 'false';
        switchInput.addEventListener('change', (e) => {
            inputElement.dataset.disabled = switchInput.checked ? 'false' : 'true';
        });

        formGroup.appendChild(inputElement);

        // 使用 setTimeout 确保DOM元素完全渲染后再应用默认值
        setTimeout(() => {
            // 应用默认值和预设值的优先级处理
            // 优先级：预设值 > 默认值 > 空值
            let finalValue: any = undefined;
            const presetValue = this.parameterValues[param.name];

            // 确定最终使用的值
            if (presetValue !== undefined) {
                finalValue = presetValue;
                this.logService.debug(`参数 ${param.name} 使用预设值: ${presetValue}`);
            } else if (param.default !== undefined && param.default !== null) {
                finalValue = param.default;
                this.logService.debug(`参数 ${param.name} 使用默认值: ${param.default}`);
            }

            // 应用最终值到控件
            if (finalValue !== undefined) {
                this.logService.info(`准备为参数 ${param.name} 应用默认值: ${finalValue}`);
                this.applyValueToControl(doc, formGroup, param, finalValue);
            } else {
                this.logService.debug(`参数 ${param.name} 没有默认值或预设值，跳过填充`);
            }
        }, 0);

        container.appendChild(formGroup);
    }


    /**
     * 创建参数表单组
     */
    private createParameterFormGroup(doc: Document, container: HTMLElement, param: any): void {
        // 调试：打印参数基本信息（name 和 paramname）
        this.logService.info(`创建参数表单组: name=${param.name}, paramname=${param.paramname}, type=${param.type}`);
        // 创建表单项容器
        const formGroup = doc.createElement('div');
        formGroup.className = 'form-group';
        formGroup.dataset.paramName = param.name;
        this.appendCondictions(formGroup, param);

        // 创建标签
        const label = doc.createElement('label');
        label.htmlFor = `param-${param.name}`;
        label.className = 'param-label';
        label.textContent = param.AdvancePramname || param.paramname || param.name;

        // 如果是必填参数，添加星号
        if (param.required === true || param.required === 'True') {
            const requiredMark = doc.createElement('span');
            requiredMark.className = 'required-mark';
            requiredMark.textContent = ' *';
            label.appendChild(requiredMark);
        }

        // 如果有描述，添加提示图标
        if (param.description) {
            const tooltip = doc.createElement('span');
            tooltip.className = 'tooltip-icon';
            tooltip.textContent = 'ⓘ';
            tooltip.title = param.description;
            label.appendChild(tooltip);
        }

        formGroup.appendChild(label);

        // 针对 driver 参数，使用应用对象下拉
        if (param.name === 'driver' && this.appList && this.appList.length > 0) {
            const select = doc.createElement('select');
            select.id = `param-${param.name}`;
            select.name = param.name;
            select.className = 'form-input';

            // 使用appList中的应用对象填充下拉框
            this.appList.forEach((app) => {
                const option = doc.createElement('option');
                option.value = app.value;
                option.textContent = app.label;

                // 添加英文名称作为自定义数据属性，用于ProcessName匹配
                if (app.en) {
                    option.setAttribute('data-en', app.en);
                }

                // 使用appList中的selected属性确定是否选中
                if (app.selected) {
                    option.selected = true;
                    this.logService.debug(`应用对象默认选中: ${app.label} (${app.value})`);
                }

                select.appendChild(option);
            });

            formGroup.appendChild(select);
            container.appendChild(formGroup);
            return;
        }

        // 根据参数类型创建不同的输入控件
        let inputElement: HTMLElement;

        // 添加调试日志
        this.logService.debug(`参数完整信息:`, param);

        // 检查参数类型是否存在
        this.logService.debug(`参数类型原始值:`, param.type, `类型:`, typeof param.type);

        // 处理特殊情况，如果 type 是字符串且包含逗号，可能是枚举值
        if (typeof param.type === 'string' && param.type.includes(',')) {
            // 将逗号分隔的字符串转换为数组，并设置为 enum_value
            param.enum_value = param.type.split(',').map((v: string) => v.trim());
            // 将类型设置为 enum
            param.type = 'enum';
            this.logService.debug(`参数类型转换为 enum，枚举值:`, param.enum_value);
        }

        const paramType = param.type ? param.type.toString().toLowerCase() : '';
        this.logService.debug(`参数类型处理后:`, paramType);

        switch (paramType) {
            case 'string':
                inputElement = this.createStringInput(doc, param, true);
                break;
            case 'int':
                inputElement = this.createNumberInput(doc, param);
                break;
            case 'bool':
            case 'boolean':
                inputElement = this.createBooleanInput(doc, param);
                break;
            case 'enum':
                inputElement = this.createEnumInput(doc, param);
                break;
            case 'list':
                inputElement = this.createListInput(doc, param);
                break;
            default:
                // 处理复杂参数类型或类型不存在的情况
                inputElement = this.createComplexInput(doc, param);
        }

        formGroup.appendChild(inputElement);

        // 使用 setTimeout 确保DOM元素完全渲染后再应用默认值
        setTimeout(() => {
            // 应用默认值和预设值的优先级处理
            // 优先级：预设值 > 默认值 > 空值
            let finalValue: any = undefined;
            const presetValue = this.parameterValues[param.name];

            // 确定最终使用的值
            if (presetValue !== undefined) {
                finalValue = presetValue;
                this.logService.debug(`参数 ${param.name} 使用预设值: ${presetValue}`);
            } else if (param.default !== undefined && param.default !== null) {
                finalValue = param.default;
                this.logService.debug(`参数 ${param.name} 使用默认值: ${param.default}`);
            }

            // 应用最终值到控件
            if (finalValue !== undefined) {
                this.logService.info(`准备为参数 ${param.name} 应用默认值: ${finalValue}`);
                this.applyValueToControl(doc, formGroup, param, finalValue);
            } else {
                this.logService.debug(`参数 ${param.name} 没有默认值或预设值，跳过填充`);
            }
        }, 0);

        // 如果有默认值，显示默认值提示
        // if (param.default !== undefined && param.default !== null) {
        //     const defaultValueHint = doc.createElement('div');
        //     defaultValueHint.className = 'default-value-hint';
        //     defaultValueHint.style.fontSize = '0.9em';
        //     defaultValueHint.style.color = 'var(--vscode-descriptionForeground)';
        //     defaultValueHint.style.marginTop = '4px';
        //     defaultValueHint.textContent = `默认值: ${this.formatDefaultValueForDisplay(param.default)}`;
        //     formGroup.appendChild(defaultValueHint);
        // }

        container.appendChild(formGroup);
    }

    /**
     * 应用默认值到表单控件
     */
    private applyDefaultValuesToForm(doc: Document, defaultValues: Record<string, any>): void {
        this.logService.info('开始应用默认值到表单控件:', defaultValues);

        for (const [paramName, defaultValue] of Object.entries(defaultValues)) {
            // 查找对应的表单组
            const formGroup = doc.querySelector(`[data-param-name="${paramName}"]`) as HTMLElement;
            if (formGroup) {
                this.logService.debug(`为参数 ${paramName} 应用默认值: ${defaultValue}`);

                // 创建一个模拟的参数对象
                const param = { name: paramName };

                // 应用值到控件
                this.applyValueToControl(doc, formGroup, param, defaultValue);
            } else {
                this.logService.warn(`未找到参数 ${paramName} 对应的表单组`);
            }
        }

        // 应用值后更新参数值
        this.updateParameterValues(doc);

        this.logService.info('默认值应用完成');
    }

    /**
     * 应用值到指定的控件
     */
    private applyValueToControl(doc: Document, formGroup: HTMLElement, param: any, value: any): void {
        this.logService.info(`开始为参数 ${param.name} 应用值: ${JSON.stringify(value)}, 参数类型: ${param.type}`);

        // 处理不同类型的控件
        const control: any = formGroup.querySelector(`[name="${param.name}"]`) as HTMLInputElement | HTMLSelectElement | null;

        if (control) {
            this.logService.info(`参数 ${param.name} 找到控件: ${control.tagName}, type: ${typeof control}`);

            if (control instanceof HTMLInputElement) {
                if (control.type === 'checkbox') {
                    // 布尔类型的处理
                    const boolValue = this.convertToBoolean(value);
                    control.checked = boolValue;
                    this.logService.info(`设置 checkbox ${param.name} = ${control.checked} (原值: ${value})`);
                } else {
                    // 字符串、数字等类型的处理
                    const stringValue = this.convertToString(value);
                    control.value = stringValue;
                    this.logService.info(`设置 input ${param.name} = "${control.value}" (原值: ${value})`);
                }
            } else if (control instanceof HTMLSelectElement) {
                // 下拉选择框的处理
                const stringValue = this.convertToString(value);

                // 记录所有可用选项
                const options = Array.from(control.options).map(opt => opt.value);
                this.logService.info(`下拉框 ${param.name} 可用选项: [${options.join(', ')}]`);
                this.logService.info(`准备设置下拉框 ${param.name}，目标值: "${stringValue}" (原始值: ${JSON.stringify(value)}, 类型: ${typeof value})`);

                // 特殊处理布尔值默认值
                if (typeof value === 'boolean' || value === 'True' || value === 'False') {
                    // 尝试不同的布尔值字符串格式
                    const possibleValues = [
                        stringValue,                    // 'true' or 'false'
                        stringValue.toLowerCase(),      // 确保小写
                        stringValue.charAt(0).toUpperCase() + stringValue.slice(1), // 'True' or 'False'
                        value === true || value === 'True' ? 'True' : 'False',     // YAML格式
                        value === true || value === 'True' ? '1' : '0'             // 数字格式
                    ];

                    let matched = false;
                    for (const testValue of possibleValues) {
                        if (options.includes(testValue)) {
                            control.value = testValue;
                            matched = true;
                            this.logService.info(`下拉框 ${param.name} 成功匹配布尔值 "${testValue}"`);
                            break;
                        }
                    }

                    if (!matched) {
                        this.logService.warn(`下拉框 ${param.name} 未能匹配布尔值，尝试过的值: [${possibleValues.join(', ')}]`);
                        // 作为最后的尝试，直接设置字符串值
                        control.value = stringValue;
                    }
                } else {
                    // 非布尔值，直接设置
                    control.value = stringValue;
                }

                this.logService.info(`设置 select ${param.name} = "${control.value}" (selectedIndex: ${control.selectedIndex})`);

                // 如果没有找到匹配的选项，记录警告
                if (control.selectedIndex === -1 && control.options.length > 0) {
                    this.logService.warn(`下拉框 ${param.name} 没有找到匹配值 "${control.value}" 的选项，可用选项: [${options.join(', ')}]`);
                } else {
                    this.logService.info(`下拉框 ${param.name} 成功选中索引: ${control.selectedIndex}`);
                }
            } else if (control?.type === 'number') {
                const numValue = this.convertToFloat(value);
                control.value = numValue.toString();
                control.dispatchEvent(new Event('input'));
                this.logService.info(`设置 number ${param.name} = "${control.value}" (原值: ${value})`);
            }
        } else {
            this.logService.warn(`未找到参数 ${param.name} 对应的控件元素`);
        }

        // 处理特殊类型的控件（如列表类型）
        if (param.type === 'list' && Array.isArray(value)) {
            this.logService.info(`处理列表类型参数 ${param.name}`);
            this.applyListValue(doc, formGroup, param, value);
        }

        // 处理复杂类型的控件（如位置、对象等）
        if (param.name === 'key' || param.paramname?.includes('对象') || param.paramname?.includes('元素')) {
            this.logService.info(`处理复杂类型参数 ${param.name}`);
            this.applyComplexValue(doc, formGroup, param, value);
        }

        this.logService.info(`完成为参数 ${param.name} 应用值`);
    }

    /**
     * 应用列表值到列表控件
     */
    private applyListValue(doc: Document, formGroup: HTMLElement, param: any, value: any[]): void {
        const listContainer = formGroup.querySelector('.list-container') as HTMLElement;
        if (!listContainer) {
            this.logService.warn(`未找到参数 ${param.name} 的列表容器`);
            return;
        }

        const itemsContainer = listContainer.querySelector('.list-items-container') as HTMLElement;
        if (!itemsContainer) {
            this.logService.warn(`未找到参数 ${param.name} 的列表项容器`);
            return;
        }

        // 清空现有项
        itemsContainer.replaceChildren();

        // 添加列表项
        value.forEach((itemValue: any, index: number) => {
            const itemGroup = doc.createElement('div');
            itemGroup.className = 'list-item-group';

            const input = doc.createElement('input');
            input.type = 'text';
            input.className = 'form-input';
            input.name = `${param.name}[${index}]`;
            input.value = this.convertToString(itemValue);
            input.addEventListener('input', () => this.updateParameterValues(doc));

            const removeBtn = doc.createElement('button');
            removeBtn.type = 'button';
            removeBtn.className = 'capture-button';
            removeBtn.textContent = '-';
            removeBtn.addEventListener('click', () => {
                itemGroup.remove();
                this.updateParameterValues(doc);
            });

            itemGroup.appendChild(input);
            itemGroup.appendChild(removeBtn);
            itemsContainer.appendChild(itemGroup);
        });

        this.logService.debug(`设置列表 ${param.name}，包含 ${value.length} 个项目`);
    }

    /**
     * 应用复杂值到复杂控件
     */
    private applyComplexValue(doc: Document, formGroup: HTMLElement, param: any, value: any): void {
        // 处理 key 参数或元素对象参数的特殊逻辑
        const input = formGroup.querySelector(`[name="${param.name}"]`) as HTMLInputElement;
        if (input) {
            input.value = this.convertToString(value);
            this.logService.debug(`设置复杂控件 ${param.name} = ${input.value}`);
        }

        // 如果有隐藏的 datamap 输入，也要设置
        const datamapInput = formGroup.querySelector(`#param-${param.name}-datamap`) as HTMLInputElement;
        if (datamapInput && typeof value === 'object') {
            datamapInput.value = JSON.stringify(value);
            this.logService.debug(`设置 datamap ${param.name} = ${datamapInput.value}`);
        }
    }

    /**
     * 转换值为布尔类型
     */
    private convertToBoolean(value: any): boolean {
        if (typeof value === 'boolean') {
            return value;
        }
        if (typeof value === 'string') {
            const lowerValue = value.toLowerCase();
            // 支持多种布尔值字符串格式，包括YAML中常见的'True'/'False'
            return lowerValue === 'true' || lowerValue === '1' || lowerValue === 'yes' || value === 'True';
        }
        if (typeof value === 'number') {
            return value !== 0;
        }
        return Boolean(value);
    }

    /**
     * 转换值为字符串类型
     */
    private convertToString(value: any): string {
        if (value === null || value === undefined) {
            return '';
        }
        if (typeof value === 'string') {
            return value;
        }
        if (typeof value === 'boolean') {
            // 确保布尔值转换为小写字符串，与select的option值匹配
            return value ? 'true' : 'false';
        }
        if (typeof value === 'object') {
            return JSON.stringify(value);
        }
        return String(value);
    }

    /** 转换为float类型 */
    private convertToFloat(value: any, defaultValue = 0): number {
        try {
            if (typeof value === 'number') {
                return value;
            }
            if (typeof value === 'string') {
                const newValue = parseFloat(value);
                if (!isNaN(newValue)) {
                    return newValue;
                }
            }
        } catch (err) {
            this.logService.error(`转换$ {value} 为float类型失败: ${err}`);
        }
        return defaultValue;
    }

    /**
     * 格式化默认值用于显示
     */
    private formatDefaultValueForDisplay(value: any): string {
        if (value === null) {
            return 'null';
        }
        if (value === undefined) {
            return 'undefined';
        }
        if (typeof value === 'string') {
            return `"${value}"`;
        }
        if (typeof value === 'boolean') {
            return value ? 'true' : 'false';
        }
        if (Array.isArray(value)) {
            return `[${value.map(v => this.formatDefaultValueForDisplay(v)).join(', ')}]`;
        }
        if (typeof value === 'object') {
            return JSON.stringify(value);
        }
        return String(value);
    }

    /**
     * 创建复杂参数输入控件
     */
    private createComplexInput(doc: Document, param: any): HTMLElement {
        // 检查参数类型是否为object
        const paramType = param.type ? param.type.toString().toLowerCase() : '';
        if (paramType === 'object') {
            this.logService.info(`参数 ${param.name} 是object类型，创建return_var下拉选择控件`);
            return this.createObjectReturnVarInput(doc, param);
        }

        // 对于元素对象参数（标签包含对象或元素），使用带下拉的输入和捕获按钮
        if (typeof param.paramname === 'string' && (param.paramname.includes('对象') || param.paramname.includes('元素'))) {
            this.logService.info('使用 createKeyInputWithDropdown 创建元素对象下拉及捕获控件');
            return this.createKeyInputWithDropdown(doc, param);
        }
        // 根据参数特性决定使用哪种控件
        if (param.position && param.position.length > 0) {
            return this.createPositionInput(doc, param);
        } else if (param.selected && param.selected.length > 0) {
            return this.createMultiSelectInput(doc, param);
        } else if (param.enum_value && (typeof param.enum_value === 'string' || param.enum_value.length > 0)) {
            return this.createEnumInput(doc, param);
        } else {
            // 默认使用字符串输入
            return this.createStringInput(doc, param);
        }
    }

    /**
     * 创建阈值输入控件
     */
    private createThresholdInput(doc: Document, param: any): HTMLElement {
        const container = doc.createElement('div');
        container.className = 'threshold-container';

        // 进度条容器
        const progressContainer = doc.createElement('div');
        progressContainer.className = 'threshold-progress-container';

        // 进度条
        const progressBar = doc.createElement('div');
        progressBar.className = 'threshold-progress-bar';
        const defaultValue = this.convertToFloat(param.default, 0.8);
        progressBar.style.width = `${defaultValue * 100}%`;
        progressContainer.appendChild(progressBar);

        // 数值输入框
        const valueInput = doc.createElement('input');
        valueInput.id = `param-${param.name}`;
        valueInput.name = param.name;
        valueInput.className = 'form-input threshold-progress-input';
        valueInput.type = 'number';
        valueInput.min = '0';
        valueInput.max = '1';
        valueInput.step = '0.1';
        valueInput.value = defaultValue.toString();

        // 数值同步
        valueInput.addEventListener('input', () => {
            const value = parseFloat(valueInput.value);
            // 验证输入值有效性
            if (!isNaN(value) && value >= 0 && value <= 1) {
                progressBar.style.width = `${value * 100}%`;
            }
        });

        let isDragging = false;
        const handleMouseDown = (e: MouseEvent) => {
            isDragging = true;
            updateProgress(e);
            doc.addEventListener('mousemove', handleMouseMove);
            doc.addEventListener('mouseup', handleMouseUp);
        };

        const handleMouseMove = (e: MouseEvent) => {
            if (isDragging) {
                updateProgress(e);
            }
        };

        const handleMouseUp = () => {
            isDragging = false;
            doc.removeEventListener('mousemove', handleMouseMove);
            doc.removeEventListener('mouseup', handleMouseUp);
        };

        const updateProgress = (e: MouseEvent) => {
            const rect = progressContainer.getBoundingClientRect();
            const percentage = Math.max(0, Math.min(1, (e.clientX - rect.left) / rect.width));
            const value = Math.round(percentage * 10) / 10; // 保留一位小数，匹配step=0.1
            progressBar.style.width = `${value * 100}%`;
            valueInput.value = value.toString();
        };
        progressContainer.addEventListener('mousedown', handleMouseDown);

        container.appendChild(progressContainer);
        container.appendChild(valueInput);
        return container;
    }

    private showInsertReturnVar(doc: Document, returnVarButton: HTMLElement) {
        const popup = doc.querySelector('.insert-return-var-modal') as HTMLElement;
        if (!popup) {
            this.logService.error(`全局变量浮窗 insert-return-var-modal 未找到，弹窗失败`);
            return;
        }
        while (popup.firstChild) {
            popup.removeChild(popup.firstChild);
        }

        const variables = MethodWindowEventHandler.getInsertedReturnVars();
        if (variables.length > 0) {

            // 创建表格元素
            const table = doc.createElement('table');
            table.className = 'variable-table';

            // 创建表头
            const thead = doc.createElement('thead');
            const headerRow = doc.createElement('tr');
            const nameHeader = doc.createElement('th');
            nameHeader.textContent = '变量名';
            const descHeader = doc.createElement('th');
            descHeader.textContent = '描述';
            headerRow.appendChild(nameHeader);
            headerRow.appendChild(descHeader);
            thead.appendChild(headerRow);
            table.appendChild(thead);
            const tbody = doc.createElement('tbody');

            // 添加变量选项
            variables.forEach(item => {
                const row = doc.createElement('tr');
                row.className = 'variable-table-row';

                // 变量名单元格
                const nameCell = doc.createElement('td');
                nameCell.textContent = item.name;
                nameCell.className = 'variable-name-cell';

                // 描述单元格
                const descCell = doc.createElement('td');
                descCell.textContent = item.description || '';
                descCell.className = 'variable-desc-cell';

                row.appendChild(nameCell);
                row.appendChild(descCell);

                // 行点击事件
                row.addEventListener('click', () => {
                    const input = returnVarButton?.parentElement?.querySelector('input');
                    if (input) {
                        const rawValue = input?.value || '';
                        input.value = rawValue + `$\{${item.name}\}`;;
                        input.dispatchEvent(new Event('input'));
                    }
                    popup.style.display = 'none';
                });

                tbody.appendChild(row);
            });
            table.appendChild(tbody);
            popup.appendChild(table);
        } else {
            const itemEl = doc.createElement('div');
            itemEl.textContent = '没有可添加的全局参数';
            popup.appendChild(itemEl);
        }

        const closeButtonContainer = doc.createElement('div');
        closeButtonContainer.className = 'insert-return-var-modal-close-container';
        const closeButton = doc.createElement('button');
        closeButton.className = 'insert-return-var-modal-close-button';
        closeButton.textContent = '关闭';
        closeButtonContainer.appendChild(closeButton);
        closeButton.addEventListener('click', () => {
            popup.style.display = 'none';
        });
        popup.appendChild(closeButtonContainer);

        popup.style.display = 'block';
    }

    private appendInsertReturnVarButton(doc: Document, inputElement: HTMLElement): HTMLElement {
        const insertedReturnVars = MethodWindowEventHandler.getInsertedReturnVars();
        if (insertedReturnVars.length === 0) {
            return inputElement;
        }

        const container = doc.createElement('div');
        container.className = 'returnvar-button-container';
        container.appendChild(inputElement);
        const returnVarButton = doc.createElement('button');
        returnVarButton.type = 'button';
        returnVarButton.className = 'refresh-returnvar-button';
        returnVarButton.textContent = '{x}';
        returnVarButton.title = '选择全局变量';

        returnVarButton.addEventListener('click', () => {
            // 打开选择全局变量输入框
            this.showInsertReturnVar(doc, returnVarButton);
        });
        container.appendChild(returnVarButton);
        return container;
    }

    /**
     * 创建字符串输入控件
     */
    private createStringInput(doc: Document, param: any, canAppendInsert: boolean = false): HTMLElement {
        // 对于元素对象参数（标签包含对象或元素），使用带下拉的输入和捕获按钮
        if (typeof param.paramname === 'string' && (param.paramname.includes('对象') || param.paramname.includes('元素'))) {
            this.logService.info('使用 createKeyInputWithDropdown 创建元素对象下拉及捕获控件');
            return this.createKeyInputWithDropdown(doc, param);
        }
        // 对于 key 参数且提供了可选类型，使用带下拉的输入 (类型下拉 + 捕获按钮)
        if (param.name === 'key' && param.selected) {
            this.logService.info('使用 createKeyInputWithDropdown 创建 key 参数下拉控件');
            return this.createKeyInputWithDropdown(doc, param);
        }

        if (param.name === 'threshold') {
            this.logService.info('使用 createThresholdInput 创建进度条控件');
            return this.createThresholdInput(doc, param);
        }

        // 创建文本输入
        const input = doc.createElement('input');
        input.type = 'text';
        input.id = `param-${param.name}`;
        input.name = param.name;
        input.className = 'form-input';
        // 默认值现在在 createParameterFormGroup 中统一处理，此处不再重复设置
        // 收集可选建议列表（来自 param.selected 或 param.enum_value）
        let suggestions: string[] = [];
        if (Array.isArray(param.selected) && param.selected.length > 0) {
            suggestions = param.selected;
        } else if (Array.isArray(param.enum_value) && param.enum_value.length > 0) {
            suggestions = param.enum_value;
        }
        // 如果有建议列表，使用 HTML datalist 实现下拉建议
        if (suggestions.length > 0) {
            const container = doc.createElement('div');
            container.className = 'string-input-container';
            container.style.position = 'relative';
            container.appendChild(input);
            const dataList = doc.createElement('datalist');
            const dataListId = `datalist-${param.name}`;
            dataList.id = dataListId;
            suggestions.forEach(val => {
                const option = doc.createElement('option');
                option.value = val;
                dataList.appendChild(option);
            });
            input.setAttribute('list', dataListId);
            container.appendChild(dataList);
            return container;
        }

        if (canAppendInsert) {
            return this.appendInsertReturnVarButton(doc, input);
        }
        return input;
    }

    /**
     * 创建数字输入控件
     */
    private createNumberInput(doc: Document, param: any): HTMLElement {
        const input = doc.createElement('input');
        input.type = 'number';
        input.id = `param-${param.name}`;
        input.name = param.name;
        input.className = 'form-input';

        // 默认值现在在 createParameterFormGroup 中统一处理，此处不再重复设置

        return input;
    }

    /**创建bool的checkbox group */
    private createBooleanCheckboxGroup(doc: Document, param: any): HTMLElement {
        // 创建复选框组容器
        const container = doc.createElement('div');
        container.className = 'form-radio-group';

        // 创建True单选按钮
        const trueRadio = doc.createElement('input');
        trueRadio.type = 'radio';
        trueRadio.className = 'form-radio';
        trueRadio.id = `param-${param.name}-true`;
        trueRadio.name = param.name; // 相同name确保互斥
        trueRadio.value = 'true';

        const trueLabel = doc.createElement('label');
        trueLabel.htmlFor = `param-${param.name}-true`;
        trueLabel.textContent = 'True';
        trueLabel.className = 'form-radio-label';

        // 创建False单选按钮
        const falseRadio = doc.createElement('input');
        falseRadio.type = 'radio';
        falseRadio.className = 'form-radio';
        falseRadio.id = `param-${param.name}-false`;
        falseRadio.name = param.name; // 相同name确保互斥
        falseRadio.value = 'false';

        const falseLabel = doc.createElement('label');
        falseLabel.htmlFor = `param-${param.name}-false`;
        falseLabel.textContent = 'False';
        falseLabel.className = 'form-radio-label';

        container.appendChild(trueRadio);
        container.appendChild(trueLabel);
        container.appendChild(falseRadio);
        container.appendChild(falseLabel);

        return container;
    }

    /**
     * 创建布尔输入控件
     */
    private createBooleanInput(doc: Document, param: any): HTMLElement {
        // 检查是否需要创建选择框而不是复选框
        // 某些布尔参数可能需要显示为下拉选择框以便更清晰地表达true/false选择
        const name = param?.AdvancePramname || param?.paramname;
        const needsSelectBox = name && (
            name.includes('是否') ||
            name.includes('存在') ||
            name.includes('相等') ||
            name.includes('判断') ||
            name.includes('预期')
        );

        if (needsSelectBox) {
            // // 为布尔类型创建下拉选择框
            // const select = doc.createElement('select');
            // select.id = `param-${param.name}`;
            // select.name = param.name;
            // select.className = 'form-select';

            // // 添加布尔选项
            // const trueOption = doc.createElement('option');
            // trueOption.value = 'true';
            // trueOption.textContent = 'True';
            // select.appendChild(trueOption);

            // const falseOption = doc.createElement('option');
            // falseOption.value = 'false';
            // falseOption.textContent = 'False';
            // select.appendChild(falseOption);

            // this.logService.debug(`为布尔参数 ${param.name} 创建选择框，选项: [true, false]`);
            // return select;

            return this.createBooleanCheckboxGroup(doc, param);

        } else {
            // 创建传统的复选框
            const container = doc.createElement('div');
            container.className = 'checkbox-container';

            const input = doc.createElement('input');
            input.type = 'checkbox';
            input.id = `param-${param.name}`;
            input.name = param.name;
            input.className = 'form-checkbox';

            container.appendChild(input);
            return container;
        }
    }

    /**
     * 创建枚举输入控件
     */
    private createEnumInput(doc: Document, param: any): HTMLElement {
        const select = doc.createElement('select');
        select.id = `param-${param.name}`;
        select.name = param.name;
        select.className = 'form-select';

        // 只对on和off做特殊处理，避免YAML关键字冲突
        const needQuotesValues = ['on', 'off'];

        // 添加选项
        if (param.enum_value && param.enum_value.length > 0) {
            // 处理 enum_value 可能是字符串的情况
            let enumValues = param.enum_value;
            if (typeof enumValues === 'string') {
                enumValues = enumValues.split(',').map((v: string) => v.trim());
            } else if (!Array.isArray(enumValues)) {
                this.logService.warn(`参数 ${param.name} 的 enum_value 不是数组或字符串: ${typeof enumValues}`);
                enumValues = [];
            }

            enumValues.forEach((value: string) => {
                const option = doc.createElement('option');
                option.value = value;

                // 只对on和off添加引号，其他值保持原样
                if (needQuotesValues.includes(value.toLowerCase())) {
                    option.textContent = `"${value}"`;
                    // 同时存储引号版本作为实际值，确保生成的YAML是带引号的
                    option.value = `"${value}"`;
                    this.logService.debug(`枚举选项 ${value} 需要引号，显示为 "${value}"`);
                } else {
                    option.textContent = value;
                }

                select.appendChild(option);
            });
        }

        return select;
    }

    /**
     * 创建位置输入控件
     */
    private createPositionInput(doc: Document, param: any): HTMLElement {
        const container = doc.createElement('div');
        container.className = 'position-container';

        // 如果有position子参数
        if (param.position && param.position.length > 0) {
            // 创建位置输入组
            param.position.forEach((posParam: any) => {
                const posGroup = doc.createElement('div');
                posGroup.className = 'position-group';

                const posLabel = doc.createElement('label');
                posLabel.htmlFor = `param-${param.name}-${posParam.name}`;
                posLabel.className = 'position-label';
                posLabel.textContent = posParam.name;

                const posInput = doc.createElement('input');
                posInput.type = posParam.type === 'int' ? 'number' : 'text';
                posInput.id = `param-${param.name}-${posParam.name}`;
                posInput.name = `${param.name}-${posParam.name}`;
                posInput.className = 'position-input';

                posGroup.appendChild(posLabel);
                posGroup.appendChild(posInput);
                container.appendChild(posGroup);
            });
        }

        // 添加捕获元素按钮
        const captureButton = doc.createElement('button');
        captureButton.type = 'button';
        captureButton.className = 'capture-button';
        captureButton.textContent = '捕获元素';
        captureButton.addEventListener('click', () => {
            if (this.methodWindow && !this.methodWindow.closed) {
                this.captureHandler.handle(this.methodWindow, doc, param.name, 'POSITION');
            }
        });
        container.appendChild(captureButton);

        // 添加隐藏输入，存储原始POSITION JSON
        const datamapInput = doc.createElement('input');
        datamapInput.type = 'hidden';
        datamapInput.id = `param-${param.name}-datamap`;
        datamapInput.name = `param-${param.name}-datamap`;
        container.appendChild(datamapInput);

        return container;
    }

    /**
     * 创建多选类型输入控件
     */
    private createMultiSelectInput(doc: Document, param: any): HTMLElement {
        const container = doc.createElement('div');
        container.className = 'multi-select-container';

        // 如果有selected选项
        if (param.selected && param.selected.length > 0) {
            // 创建单选按钮组
            const radioGroup = doc.createElement('div');
            radioGroup.className = 'radio-group';

            param.selected.forEach((value: string, index: number) => {
                const radioContainer = doc.createElement('div');
                radioContainer.className = 'radio-container';

                const radio = doc.createElement('input');
                radio.type = 'radio';
                radio.id = `param-${param.name}-${value}`;
                radio.name = param.name;
                radio.value = value;
                radio.className = 'form-radio';

                // 默认选中第一项
                if (index === 0) {
                    radio.checked = true;
                }

                const radioLabel = doc.createElement('label');
                radioLabel.htmlFor = `param-${param.name}-${value}`;
                radioLabel.textContent = value;

                radioContainer.appendChild(radio);
                radioContainer.appendChild(radioLabel);
                radioGroup.appendChild(radioContainer);
            });

            container.appendChild(radioGroup);
        }

        return container;
    }

    /**
     * 验证必填参数
     */
    private validateRequiredParameters(doc: Document): boolean {
        if (!this.selectedMethod) {
            return false;
        }

        const methodInfo = this.methodDataLoader.getMethodDetail(this.selectedMethod);
        if (!methodInfo || !methodInfo.parameters) {
            return true; // 没有参数，视为验证通过
        }

        // 检查每个必填参数
        for (const param of methodInfo.parameters) {
            if (!param.required || param.required === 'False' || param.required === 'false' || param.show === false || param.show === 'False') {
                continue; // 非必填或不显示的参数跳过
            }

            const formGroup = doc.querySelector(`.form-group[data-param-name="${param.name}"]`);
            if (!formGroup) {
                continue; // 找不到表单项，跳过
            }

            // 根据参数类型检查值
            switch (param.type.toLowerCase()) {
                case 'string':
                case 'int':
                    const input = formGroup.querySelector(`.form-input`) as HTMLInputElement;
                    if (!input || !input.value.trim()) {
                        return false;
                    }
                    break;

                case 'enum':
                    const select = formGroup.querySelector(`.form-select`) as HTMLSelectElement;
                    if (!select || !select.value) {
                        return false;
                    }
                    break;

                default:
                    // 对于位置参数，检查所有子输入
                    if (param.position && param.position.length > 0) {
                        const positionInputs = formGroup.querySelectorAll('.position-input') as NodeListOf<HTMLInputElement>;
                        for (const posInput of positionInputs) {
                            if (!posInput.value.trim()) {
                                return false;
                            }
                        }
                    }

                    // 对于多选参数，检查是否有选中项
                    if (param.selected && param.selected.length > 0) {
                        const radios = formGroup.querySelectorAll('.form-radio:checked') as NodeListOf<HTMLInputElement>;
                        if (radios.length === 0) {
                            return false;
                        }
                    }
            }
        }

        return true;
    }

    /**
     * 更新参数值
     */
    private updateParameterValues(doc: Document): void {
        // 收集所有参数值
        const paramValues: Record<string, any> = {};

        // 处理文本和数字输入
        doc.querySelectorAll('.form-input').forEach((element) => {
            const input = element as HTMLInputElement;
            if (input.type === 'number') {
                // 对于数字输入，如果值为空，保持为 undefined，不转换为 0
                if (input.value.trim() === '') {
                    paramValues[input.name] = undefined;
                } else {
                    paramValues[input.name] = Number(input.value);
                }
            } else {
                if (input?.dataset?.disabled && input.dataset.disabled === 'true') {
                    this.logService.debug(`参数 ${input.name} 的值 ${input.value} 被disabled`);
                } else {
                    paramValues[input.name] = input.value;
                }
            }
        });

        // 处理复选框
        doc.querySelectorAll('.form-checkbox').forEach((element) => {
            const checkbox = element as HTMLInputElement;
            paramValues[checkbox.name] = checkbox.checked;
        });

        // 处理下拉选择
        doc.querySelectorAll('.form-select').forEach((element) => {
            const select = element as HTMLSelectElement;
            paramValues[select.name] = select.value;
        });

        // 处理key类型下拉菜单
        doc.querySelectorAll('.key-type-select').forEach((element) => {
            const select = element as HTMLSelectElement;
            const paramName = select.id.replace('param-', '').replace('-type', '');
            // 将key类型保存到参数中，但不作为主参数
            if (!paramValues[paramName]) {
                paramValues[paramName] = {};
            } else if (typeof paramValues[paramName] !== 'object') {
                // 如果已经是字符串，转换为对象
                const value = paramValues[paramName];
                paramValues[paramName] = { value };
            }

            // 将类型保存到参数中
            paramValues[paramName].type = select.value;
        });

        // 处理单选按钮
        const radioGroups = new Set<string>();
        doc.querySelectorAll('.form-radio').forEach((element) => {
            const radio = element as HTMLInputElement;
            radioGroups.add(radio.name);
        });

        radioGroups.forEach(groupName => {
            const checkedRadio = doc.querySelector(`.form-radio[name="${groupName}"]:checked`) as HTMLInputElement;
            if (checkedRadio) {
                paramValues[groupName] = checkedRadio.value;
            }
        });

        // 处理位置输入
        const positionParams: Record<string, Record<string, any>> = {};
        doc.querySelectorAll('.position-input').forEach((element) => {
            const input = element as HTMLInputElement;
            const [paramName, posName] = input.name.split('-');
            if (!positionParams[paramName]) {
                positionParams[paramName] = {};
            }
            if (input.type === 'number') {
                // 对于位置参数的数字输入，如果值为空，保持为 undefined
                if (input.value.trim() === '') {
                    positionParams[paramName][posName] = undefined;
                } else {
                    positionParams[paramName][posName] = Number(input.value);
                }
            } else {
                positionParams[paramName][posName] = input.value;
            }
        });

        // 合并位置参数到主参数对象
        Object.keys(positionParams).forEach(paramName => {
            paramValues[paramName] = positionParams[paramName];
        });

        // 处理列表输入
        const listValues: Record<string, any[]> = {};
        Object.keys(paramValues).forEach(key => {
            const m = key.match(/^(.+)\[(\d+)\]$/);
            if (m) {
                const name = m[1];
                const idx = parseInt(m[2], 10);
                if (!listValues[name]) listValues[name] = [];
                listValues[name][idx] = paramValues[key];
                delete paramValues[key];
            }
        });
        Object.entries(listValues).forEach(([name, arr]) => {
            if (arr) {
                const realArr = arr.filter(item => item !== undefined && item !== '');
                if (realArr) {
                    paramValues[name] = realArr;
                }
            }
        });

        // 收集隐藏的 datamap 字段（UNI 控件信息）
        doc.querySelectorAll<HTMLInputElement>('input[type="hidden"][name$="-datamap"]').forEach(i => {
            paramValues[i.name] = i.value;
        });

        // 存储参数值
        this.parameterValues = paramValues;

        // 详细记录参数值
        this.logService.info('参数值已更新，详细信息如下:');
        this.logService.info('参数总数:', Object.keys(paramValues).length);

        // 记录每个参数的键值对
        Object.entries(paramValues).forEach(([key, value]) => {
            if (typeof value === 'object' && value !== null) {
                this.logService.info(`参数 ${key}:`, JSON.stringify(value));
            } else {
                this.logService.info(`参数 ${key}: ${value}`);
            }
        });

        this.logService.debug('参数值已更新', paramValues);

        // 初始化展示
        if (paramValues?.key?.type) {
            this.logService.debug(`根据当前选中状态: ${paramValues.key.type} 初始化页面展示`);
            this.changeFormGroupVisiable(doc, 'selected', paramValues.key.type);
        }
    }

    /**
     * 获取参数值
     * 用于在插入方法时获取收集的参数值
     */
    public getParameterValues(): Record<string, any> {
        return this.parameterValues;
    }

    /**
     * 获取格式化的参数值
     * 将参数值格式化为YAML格式，用于插入到测试用例中
     * 如果用户未填写参数，则自动使用默认值
     */
    public getFormattedParameters(): string {
        if (!this.selectedMethod) {
            this.logService.warn('未选择方法，无法格式化参数');
            return '';
        }

        const methodInfo = this.methodDataLoader.getMethodDetail(this.selectedMethod);
        if (!methodInfo) {
            this.logService.warn(`无法获取方法 ${this.selectedMethod} 的详细信息`);
            return '';
        }

        // 合并参数值和默认值
        const finalValues = this.mergeWithDefaultValues(methodInfo);

        this.logService.info(`开始格式化方法 ${this.selectedMethod} 的参数，最终参数总数: ${Object.keys(finalValues).length}`);

        // 初始化结果字符串，并根据方法描述添加注释
        let result = '';
        if (methodInfo.description && typeof methodInfo.description === 'string') {
            methodInfo.description.split('\n').forEach(line => {
                result += `# ${line.trim()}\n`;
            });
        }
        result += `- action: ${this.selectedMethod}\n`;

        // 添加driver参数（如果存在）
        if (finalValues.driver) {
            result += `  driver: ${finalValues.driver}\n`;
        }

        // 添加其他参数
        const hasOtherParams = Object.entries(finalValues).some(([key, value]) =>
            key !== 'driver' && this.shouldIncludeParameter(value)
        );

        if (hasOtherParams) {
            result += `  kwargs:\n`;

            // 遍历所有参数
            Object.entries(finalValues).forEach(([key, value]) => {
                if (key === 'driver' || !this.shouldIncludeParameter(value)) {
                    return; // 跳过driver参数（已单独处理）和空值参数
                }

                // 扁平化处理 element 对象，将 value 直接作为值
                if (value && typeof value === 'object' && 'value' in value && 'type' in value) {
                    result += `    ${key}: ${this.formatYamlValue((value as any).value)}\n`;
                    return;
                }

                // 根据参数类型格式化值 (避免type被过滤)
                const param = methodInfo.parameters.find((p: any) => p.name === key);
                const allowKeys = ['type'];
                if (!param && !allowKeys.includes(key)) {
                    return;
                }

                // 处理不同类型的参数
                if (typeof value === 'object' && value !== null) {
                    // 处理位置参数
                    result += `    ${key}:\n`;
                    if (Array.isArray(value)) {
                        // 数组类型
                        value.forEach(item => {
                            result += `      - ${this.formatYamlValue(item)}\n`;
                        });
                    } else {
                        // 对象类型（如位置坐标）
                        if (param.position && param.position.length > 0) {
                            // 位置参数格式化为数组
                            param.position.forEach((posParam: any) => {
                                const posValue = value[posParam.name];
                                if (posValue !== undefined) {
                                    result += `      - ${this.formatYamlValue(posValue)}\n`;
                                }
                            });
                        } else {
                            // 普通对象
                            Object.entries(value).forEach(([subKey, subValue]) => {
                                result += `      ${subKey}: ${this.formatYamlValue(subValue)}\n`;
                            });
                        }
                    }
                } else {
                    // 基本类型
                    result += `    ${key}: ${this.formatYamlValue(value)}\n`;
                }
            });
        }

        // 记录最终格式化结果
        this.logService.info(`参数格式化完成，结果:\n${result}`);
        return result;
    }

    /**
     * 合并用户参数值和默认值
     * 优先级：用户填写的值 > 默认值
     */
    private mergeWithDefaultValues(methodInfo: any): Record<string, any> {
        const finalValues: Record<string, any> = {};

        // 首先添加所有用户填写的值
        Object.assign(finalValues, this.parameterValues);

        this.logService.info(`合并默认值前的参数值:`, JSON.stringify(this.parameterValues));

        const canMerge = (param: any) => {
            if (!param || !param.name || !param.default) {
                return false;
            }

            if (param.default === 'None' || param.default === 'none') {
                return false;
            }

            if (param?.show === 'False' || param?.show === 'false') {
                return false;
            }

            return true;
        };

        // 然后添加未填写的参数的默认值
        if (methodInfo.parameters && Array.isArray(methodInfo.parameters)) {
            methodInfo.parameters.forEach((param: any) => {
                if (canMerge(param)) {
                    const currentValue = finalValues[param.name];
                    const isEmpty = this.isParameterEmpty(currentValue);

                    this.logService.info(`参数 ${param.name}: 当前值=${currentValue}, 默认值=${param.default}, 是否为空=${isEmpty}`);

                    // 如果用户没有填写这个参数（或填写的是空值），使用默认值
                    if (isEmpty) {
                        finalValues[param.name] = param.default;
                        this.logService.info(`为参数 ${param.name} 使用默认值: ${param.default}`);
                    } else {
                        this.logService.info(`参数 ${param.name} 保持用户填写的值: ${currentValue}`);
                    }
                }
            });

            // 过滤指定key不允许展示的数据
            const keyType = finalValues?.key?.type;
            const excludeKeys: string[] = [];
            if (keyType) {
                finalValues.type = keyType;
                methodInfo.parameters.forEach((param: any) => {
                    if (param?.condiction) {
                        let selected = undefined;
                        if (Array.isArray(param.condiction)) {
                            const selectedCondictions = param.condiction.filter((item: any) => item?.selected);
                            if (selectedCondictions.length > 0) {
                                selected = selectedCondictions[0]?.selected;
                            }
                        } else {
                            selected = param.condiction?.selected;
                        }

                        if (selected && keyType !== selected) {
                            excludeKeys.push(param.name);
                        }
                    }
                });
            }

            if (excludeKeys.length > 0) {
                excludeKeys.forEach((key: string) => {
                    if (Object.keys(finalValues).includes(key)) {
                        // 删除key
                        this.logService.info(`当前选中key类型${keyType}, 删除不满足展示条件的参数 ${key}`);
                        delete finalValues[key];
                    }
                });
            }
        }

        this.logService.info(`合并默认值后的最终参数值:`, JSON.stringify(finalValues));
        return finalValues;
    }

    /**
     * 判断参数值是否为空（应该使用默认值）
     */
    private isParameterEmpty(value: any): boolean {
        if (value === undefined || value === null) {
            return true;
        }
        if (typeof value === 'string' && value.trim() === '') {
            return true;
        }
        if (Array.isArray(value) && value.length === 0) {
            return true;
        }
        return false;
    }

    /**
     * 判断参数是否应该包含在输出中
     */
    private shouldIncludeParameter(value: any): boolean {
        // 包含有意义的值（不为空的参数）
        if (value === undefined || value === null) {
            return false;
        }
        if (typeof value === 'string' && value.trim() === '') {
            return false;
        }
        if (Array.isArray(value) && value.length === 0) {
            return false;
        }
        // 对象类型需要进一步检查
        if (typeof value === 'object' && !Array.isArray(value)) {
            return Object.keys(value).length > 0;
        }
        return true;
    }

    /**
     * 格式化YAML值
     */
    private formatYamlValue(value: any): string {
        if (value === undefined || value === null) {
            return 'null';
        }

        if (typeof value === 'boolean') {
            return value ? 'true' : 'false';
        }

        if (typeof value === 'number') {
            return value.toString();
        }

        if (typeof value === 'string') {
            // 只对on和off做特殊处理，避免YAML关键字冲突
            const needQuotesValues = ['on', 'off'];

            // 如果值本身已经带引号，直接返回
            if ((value.startsWith('"') && value.endsWith('"')) || (value.startsWith("'") && value.endsWith("'"))) {
                return value;
            }

            // 只对on和off添加双引号
            if (needQuotesValues.includes(value.toLowerCase())) {
                this.logService.debug(`值 "${value}" 需要引号处理`);
                return `"${value}"`;
            }

            // 检查是否需要引号
            if (value.includes('\n') || value.includes(':') || value.includes('#') ||
                value.match(/^\s/) || value.match(/\s$/) || value === '') {
                // 使用双引号并转义特殊字符
                return `"${value.replace(/\\/g, '\\\\').replace(/"/g, '\\"')}"`;
            }
            return value;
        }

        // 对于复杂类型，转换为JSON字符串
        return JSON.stringify(value);
    }

    /**
     * 解析参数 YAML 并应用到表单控件
     */
    public applyParameterYAML(doc: Document, yamlStr: string): void {
        this.logService.info('applyParameterYAML 调用，yamlStr=' + yamlStr);
        try {
            const values: Record<string, any> = {};
            let currentListKey: string | null = null;
            yamlStr.split('\n').forEach(line => {
                const raw = line.trim();
                if (!raw || raw === 'kwargs:') { return; }
                // 列表键启动
                if (raw.endsWith(':') && raw.indexOf(':') === raw.length - 1) {
                    const listKey = raw.slice(0, -1).trim();
                    if (listKey === 'action') { currentListKey = null; return; }
                    currentListKey = listKey;
                    values[listKey] = [];
                    return;
                }
                // 列表项
                if (raw.startsWith('-') && currentListKey) {
                    let itemVal = raw.replace(/^-+\s*/, '');
                    if (/^\d+$/.test(itemVal)) {
                        values[currentListKey].push(parseInt(itemVal, 10));
                    } else if (/^(true|false)$/i.test(itemVal)) {
                        values[currentListKey].push(itemVal.toLowerCase() === 'true');
                    } else {
                        values[currentListKey].push(itemVal);
                    }
                    return;
                }
                // 普通键值对
                currentListKey = null;
                const idx = raw.indexOf(':');
                if (idx <= 0) { return; }
                const key = raw.slice(0, idx).trim();
                if (key === 'action') { return; }
                let val: any = raw.slice(idx + 1).trim();
                if (/^\d+$/.test(val)) {
                    val = parseInt(val, 10);
                } else if (/^(true|false)$/i.test(val)) {
                    val = val.toLowerCase() === 'true';
                }
                values[key] = val;
            });
            this.logService.info('applyParameterYAML 解析结果 values=' + JSON.stringify(values));
            this.parameterValues = values;
            this.applyParameterValues(doc, values);
        } catch (e) {
            this.logService.error('解析参数 YAML 失败:', e);
        }
    }

    /**
     * 应用参数值到控件
     */
    private applyParameterValues(doc: Document, values: Record<string, any>): void {
        this.logService.info('applyParameterValues 调用，values=' + JSON.stringify(values));
        // 处理列表参数，重建列表输入项
        Object.entries(values).forEach(([key, val]) => {
            if (Array.isArray(val)) {
                const container = doc.querySelector(`.list-container[data-param-name="${key}"]`);
                if (container) {
                    const itemsContainer = container.querySelector('.list-items-container') as HTMLElement;
                    // 清空旧输入项，避免使用 innerHTML 以兼容 Trusted Types
                    itemsContainer.replaceChildren();
                    val.forEach((itemValue: any, index: number) => {
                        const itemGroup = doc.createElement('div');
                        itemGroup.className = 'list-item-group';
                        const input = doc.createElement('input');
                        input.type = 'text';
                        input.className = 'form-input';
                        input.name = `${key}[${index}]`;
                        input.value = itemValue;
                        input.addEventListener('input', () => this.updateParameterValues(doc));
                        const removeBtn = doc.createElement('button');
                        removeBtn.type = 'button';
                        removeBtn.className = 'capture-button';
                        removeBtn.textContent = '-';
                        removeBtn.addEventListener('click', () => {
                            itemGroup.remove();
                            this.updateParameterValues(doc);
                        });
                        itemGroup.appendChild(input);
                        itemGroup.appendChild(removeBtn);
                        itemsContainer.appendChild(itemGroup);
                    });
                }
                delete values[key];
            }
        });
        Object.entries(values).forEach(([key, val]) => {
            this.logService.info(`applyParameterValues: 尝试设置参数 ${key} = ${val}`);
            const input = doc.querySelector(`[name="${key}"]`) as HTMLInputElement | HTMLSelectElement | null;
            if (input) {
                this.logService.info(`applyParameterValues: 找到控件 name=${key}, type=${input.tagName}`);
            } else {
                this.logService.warn(`applyParameterValues: 未找到控件 name=${key}`);
            }
            if (input && input.tagName === 'INPUT') {
                const inputEl = input as HTMLInputElement;
                this.logService.info(`applyParameterValues: 控件为 INPUT, type=${inputEl.type}`);
                if (inputEl.type === 'checkbox') {
                    this.logService.info(`applyParameterValues: 设置 checkbox.checked = ${Boolean(val)}`);
                    inputEl.checked = Boolean(val);
                } else {
                    this.logService.info(`applyParameterValues: 设置 input.value = ${String(val)}`);
                    inputEl.value = String(val);
                }
            } else if (input && input.tagName === 'SELECT') {
                const selectEl = input as HTMLSelectElement;
                this.logService.info(`applyParameterValues: 控件为 SELECT, 设置 select.value = ${String(val)}`);
                selectEl.value = String(val);
            }
        });
    }

    /**
     * 创建列表类型输入控件Item
     */
    private createListInputItem(doc: Document, container: HTMLElement, param: any, defaultValue: string = ''): void {
        const index = container.children.length;
        const itemGroup = doc.createElement('div');
        itemGroup.className = 'list-item-group';
        const input = doc.createElement('input');
        input.type = 'text';
        input.className = 'form-input';
        input.name = `${param.name}[${index}]`;
        input.value = defaultValue;
        input.addEventListener('input', () => this.updateParameterValues(doc));
        const inputElement = this.appendInsertReturnVarButton(doc, input);

        const addBtn = doc.createElement('button');
        addBtn.type = 'button';
        addBtn.className = 'add-button';
        addBtn.textContent = '+';
        addBtn.addEventListener('click', () => {
            this.createListInputItem(doc, container, param);
        });

        const removeBtn = doc.createElement('button');
        removeBtn.type = 'button';
        removeBtn.className = 'del-button';
        removeBtn.textContent = '-';
        removeBtn.addEventListener('click', () => {
            itemGroup.remove();
            this.updateParameterValues(doc);
            if (container.children.length === 0) {
                // 增加默认输入框
                this.createListInputItem(doc, container, param);
            }
        });
        itemGroup.appendChild(inputElement);
        itemGroup.appendChild(addBtn);
        itemGroup.appendChild(removeBtn);
        container.appendChild(itemGroup);
    }
    /**
     * 创建列表类型输入控件
     */
    private createListInput(doc: Document, param: any): HTMLElement {
        const container = doc.createElement('div');
        container.setAttribute('data-param-name', param.name);
        container.className = 'list-container';
        const itemsContainer = doc.createElement('div');
        itemsContainer.setAttribute('data-param-name', param.name);
        itemsContainer.className = 'list-items-container';
        // 初始化默认列表项
        const initialItems: any[] = Array.isArray(param.default) ? param.default : [];
        initialItems.forEach((itemValue: any, index: number) => {
            this.createListInputItem(doc, itemsContainer, param, itemValue);
        });

        container.appendChild(itemsContainer);
        // 如果为空，追加一个默认的
        if (itemsContainer.children.length === 0) {
            this.createListInputItem(doc, itemsContainer, param);
        }

        return container;
    }

    /**
     * 为 key 参数创建带下拉菜单和捕获按钮的输入控件
     */
    private createKeyInputWithDropdown(doc: Document, param: any): HTMLElement {
        const selectedMap = {
            'UNI': '元素',
            'POSITION': '坐标',
            'IMAGE': '图片',
            'ICON': '图片',
            'TEXT': '文本',
            'HOTKEY': '快捷键',
        };

        // 复用元素对象输入控件（带下拉列表）作为基础
        const container = this.createObjectInputWithDropdown(doc, param);
        // 调整样式为 key-input-container
        container.classList.remove('object-input-container');
        container.classList.add('key-input-container');
        // 添加隐藏 datamap 输入
        const datamapInput = doc.createElement('input');
        datamapInput.type = 'hidden';
        datamapInput.id = `param-${param.name}-datamap`;
        datamapInput.name = `${param.name}-datamap`;
        container.appendChild(datamapInput);
        // 添加类型下拉
        const select = doc.createElement('select');
        select.className = 'key-type-select';
        select.id = `param-${param.name}-type`;
        // 遍历选项生成下拉菜单
        let selectedValues: string[] = [];
        if (typeof param.selected === 'string') {
            selectedValues = param.selected.split(',').map((v: string) => v.trim());
        } else if (Array.isArray(param.selected)) {
            selectedValues = param.selected;
        }
        // 遍历选项生成下拉菜单
        selectedValues.forEach(value => {
            const option = doc.createElement('option');
            option.value = value;

            const label = (value in selectedMap ? selectedMap[value as keyof typeof selectedMap] : value);
            option.textContent = label;
            select.appendChild(option);
        });
        container.appendChild(select);
        // 添加捕获按钮
        const captureButton = doc.createElement('button');
        captureButton.type = 'button';
        captureButton.className = 'capture-button';
        captureButton.textContent = '捕获控件';
        container.appendChild(captureButton);
        // 类型下拉变更逻辑
        select.addEventListener('change', () => {
            this.logService.info(`用户选择的key类型: ${select.value}`);
            // 根据类型控制组件的显示和隐藏
            this.changeFormGroupVisiable(doc, 'selected', select.value);
            const typeInput = doc.getElementById('param-type') as HTMLInputElement;
            if (typeInput) {
                const oldValue = typeInput.value;
                let newValue = oldValue;

                // 根据选择的元素类型设置对应的type值，与captureKeyElementHandler保持一致
                if (select.value === 'UNI') {
                    newValue = 'UNI';
                } else if (select.value === 'GAT') {
                    newValue = 'GAT';
                } else if (select.value === 'ICON' || select.value === 'REGION' || select.value === 'POSITION' || select.value === 'HOTKEY') {
                    // ICON、REGION、POSITION、HOTKEY类型归属于OCR方式
                    newValue = 'OCR';
                } else {
                    // 其他类型默认为OCR
                    newValue = 'OCR';
                }

                // 只有当值需要改变时才更新
                if (oldValue !== newValue) {
                    this.logService.info(`用户选择元素类型后自动同步type字段: ${oldValue} -> ${newValue} (基于元素类型: ${select.value})`);
                    typeInput.value = newValue;
                    // 触发change事件，确保相关逻辑能够响应
                    typeInput.dispatchEvent(new Event('change', { bubbles: true }));
                }
            } else {
                this.logService.warn('未找到type参数输入框，无法自动同步type字段');
            }
        });
        // 捕获按钮逻辑
        captureButton.addEventListener('click', () => {
            this.captureHandler.handle(this.methodWindow!, doc, param.name, select.value);
        });
        return container;
    }

    /**根据form item上的condiction条件，控制组件的显示和隐藏 */
    private changeFormGroupVisiable(doc: Document, condictionType: any, currentValue: any) {
        // 控制组件显示隐藏
        const formGroups = doc.querySelectorAll('.form-group');
        if (!formGroups) {
            return;
        }

        const key: string = `condiction_${condictionType}`;
        formGroups.forEach((item: Element) => {
            const htmlItem = item as HTMLElement;
            if (htmlItem?.dataset?.[key] && htmlItem.dataset?.[key] !== currentValue) {
                htmlItem.style.display = 'none';
                this.logService.info(`根据条件${key}=${currentValue}，隐藏组件${htmlItem.dataset}`);
            } else {
                htmlItem.style.display = 'flex';
            }
        });

        // .advance-params-container内部的.form-group
        const advanceParamsContainer = doc.querySelectorAll('.advance-params-container');
        if (!advanceParamsContainer) {
            return;
        }

        advanceParamsContainer.forEach((advanceParamsItem) => {
            let isDisplay = false;
            const advanceFormGroups = advanceParamsItem.querySelectorAll('.form-group');
            if (advanceFormGroups) {
                advanceFormGroups.forEach((item: Element) => {
                    const htmlItem = item as HTMLElement;
                    if (htmlItem.style.display === 'block' || htmlItem.style.display === 'flex') {
                        isDisplay = true;
                    }
                });
            }

            (advanceParamsItem as HTMLElement).style.display = isDisplay ? 'block' : 'none';
        });
    }

    /**
     * 新增：为元素对象参数创建带下拉菜单的输入控件
     */
    private createObjectInputWithDropdown(doc: Document, param: any): HTMLElement {
        const container = doc.createElement('div');
        container.className = 'object-input-container';
        container.style.position = 'relative';
        const input = doc.createElement('input');
        input.type = 'text';
        input.id = `param-${param.name}`;
        input.name = param.name;
        input.className = 'form-input object-input';
        if (param.default !== undefined && param.default !== null) {
            input.value = param.default.toString();
        }
        const dropdown = doc.createElement('div');
        dropdown.className = 'object-dropdown';
        container.appendChild(input);
        container.appendChild(dropdown);
        input.addEventListener('click', async e => {
            e.stopPropagation();
            this.logService.info('点击元素对象输入框，准备加载 locator 列表');
            // 每次点击清空旧下拉项并重新加载
            while (dropdown.firstChild) {
                dropdown.removeChild(dropdown.firstChild);
            }
            try {
                let locatorDir: URI | null = null;
                const configPath = this.configurationService.getValue<string>('gat.testcasePath') || '';
                if (configPath) {
                    const p = joinPath(URI.file(configPath), '..', 'locator');
                    try { await this.fileService.resolve(p); locatorDir = p; } catch { }
                }
                if (!locatorDir) {
                    const possible = [
                        joinPath(URI.file(this.environmentService.appRoot), 'extensions', 'KylinRobot-v2', 'locator'),
                        joinPath(URI.file(this.environmentService.extensionsPath), 'KylinRobot-v2', 'locator'),
                        URI.file('/usr/share/vscode/app/extensions/KylinRobot-v2/locator')
                    ];
                    for (const p of possible) {
                        try { await this.fileService.resolve(p); locatorDir = p; break; } catch { }
                    }
                }
                if (!locatorDir) { this.logService.error('未找到 locator 目录'); }
                const driver = (doc.querySelector('#param-driver') as HTMLSelectElement)?.value;
                if (!driver) { this.logService.warn('driver 未设置，无法加载 locator'); }
                // 只有在 locatorDir 和 driver 有效时才加载文件并填充下拉
                if (locatorDir && driver) {
                    // 根据 key_type 决定加载不同的 locator 文件
                    const typeSelect = doc.querySelector(`#param-${param.name}-type`) as HTMLSelectElement;
                    const selectedType = typeSelect?.value;
                    // 将 keyType 映射为 yamlType：UNI/GAT 保持不变，其它均为 OCR（POSITION归为OCR子集）
                    const yamlType = (selectedType === 'UNI' || selectedType === 'GAT') ? selectedType : 'OCR';
                    // 根据 yamlType 决定使用的 YAML 文件
                    const fileName = yamlType === 'UNI' ? `${driver}_uni.yml` : yamlType === 'GAT' ? `${driver}_gat.yml` : `${driver}.yml`;
                    let fileUri = joinPath(locatorDir, fileName);
                    let content: string;
                    try {
                        content = (await this.fileService.readFile(fileUri)).value.toString();
                    } catch {
                        // 尝试使用 .yaml 后缀
                        const yamlFileName = fileName.replace(/\.yml$/, '.yaml');
                        fileUri = joinPath(locatorDir, yamlFileName);
                        content = (await this.fileService.readFile(fileUri)).value.toString();
                    }
                    this.logService.info(`加载 locator 文件: ${fileUri.toString()}`);
                    // 使用js-yaml解析locator YAML
                    let parsedObj: Record<string, any>;
                    try {
                        parsedObj = robustYamlParse(content) as Record<string, any>;
                    } catch (e) {
                        this.logService.error('解析 locator YAML 失败:', e);
                        return;
                    }
                    // 分类型渲染：POSITION/REGION/ICON/HOTKEY 专用；GAT/UNI 一层；OCR 无限层
                    if (selectedType === 'POSITION') {
                        this.renderPositionItems(doc, dropdown, parsedObj, input);
                    } else if (selectedType === 'REGION') {
                        this.renderRegionItems(doc, dropdown, parsedObj, input);
                    } else if (selectedType === 'ICON') {
                        this.renderIconItems(doc, dropdown, parsedObj, input);
                    } else if (selectedType === 'HOTKEY') {
                        this.renderHotkeyItems(doc, dropdown, parsedObj, input);
                    } else {
                        const maxDepth = (yamlType === 'UNI' || yamlType === 'GAT') ? 1 : Infinity;
                        this.renderDropdownItems(doc, dropdown, parsedObj, input, 0, '', yamlType, maxDepth);
                    }
                }
            } catch (err) {
                this.logService.error('加载 locator 列表失败:', err);
            }
            // 展示下拉列表
            dropdown.style.display = 'block';
        });
        doc.addEventListener('click', event => { if (!container.contains(event.target as Node)) dropdown.style.display = 'none'; });
        return container;
    }

    /**
     * 为object类型参数创建return_var选择下拉框（无捕获按钮）
     */
    private createObjectReturnVarInput(doc: Document, param: any): HTMLElement {
        const container = doc.createElement('div');
        container.className = 'object-returnvar-container';

        // 创建一个包装容器，包含下拉框和刷新按钮
        const selectWrapper = doc.createElement('div');
        selectWrapper.style.display = 'flex';
        selectWrapper.style.alignItems = 'center';
        selectWrapper.style.gap = '8px';

        // 创建下拉选择框
        const select = doc.createElement('select');
        select.id = `param-${param.name}`;
        select.name = param.name;
        select.className = 'form-select';
        select.style.flex = '1';

        // 刷新下拉列表的函数
        const refreshDropdown = () => {
            // 清空现有选项
            while (select.firstChild) {
                select.removeChild(select.firstChild);
            }

            // 添加默认空选项
            const defaultOption = doc.createElement('option');
            defaultOption.value = '';
            defaultOption.textContent = '请选择return_var值';
            select.appendChild(defaultOption);

            // 添加已插入的return_var选项
            const insertedReturnVars = MethodWindowEventHandler.getInsertedReturnVars();
            this.logService.info(`刷新object参数下拉列表，可用的return_var值: ${JSON.stringify(insertedReturnVars)}`);

            insertedReturnVars.forEach((item) => {
                const option = doc.createElement('option');
                option.value = `$\{${item.name}\}`;  // 添加$前缀表示这是变量引用
                option.textContent = `${item.description || item.name}`;
                select.appendChild(option);
            });

            // 如果没有可用的return_var，显示提示
            if (insertedReturnVars.length === 0) {
                const noVarsOption = doc.createElement('option');
                noVarsOption.value = '';
                noVarsOption.textContent = '暂无可用的return_var值';
                noVarsOption.disabled = true;
                select.appendChild(noVarsOption);
            }
        };

        // 初始化下拉列表
        refreshDropdown();

        // 创建刷新按钮
        const refreshButton = doc.createElement('button');
        refreshButton.type = 'button';
        refreshButton.className = 'refresh-returnvar-button';
        refreshButton.textContent = '🔄';
        refreshButton.title = '刷新可用的return_var值';

        refreshButton.addEventListener('click', () => {
            refreshDropdown();
            this.logService.info(`手动刷新了参数 ${param.name} 的return_var下拉列表`);
        });

        selectWrapper.appendChild(select);
        selectWrapper.appendChild(refreshButton);
        container.appendChild(selectWrapper);

        // 添加提示文本
        const objectHint = doc.createElement('div');
        objectHint.className = 'object-param-hint';
        objectHint.style.fontSize = '0.85em';
        objectHint.style.color = 'var(--vscode-descriptionForeground)';
        objectHint.style.marginTop = '4px';
        objectHint.textContent = 'object类型参数只能选择已插入方法的return_var值，点击刷新按钮更新列表';
        container.appendChild(objectHint);

        return container;
    }

    /**
     * 递归渲染层级对象到下拉列表
     * @param keyType 解析类型，如 'OCR','UNI','GAT'，用于控制解析深度
     */
    private renderDropdownItems(doc: Document, dropdown: HTMLElement, data: Record<string, any>, input: HTMLInputElement, level: number = 0, prefix: string = '', keyType: string = '', maxDepth: number = Infinity): void {
        const stopNodes = ['type', 'size', 'region', 'icon'];
        for (const key in data) {
            // 当 keyType 不是 UNI 或 GAT（即 OCR 模式）时隐藏 stopNodes 节点
            if (keyType !== 'UNI' && keyType !== 'GAT' && stopNodes.includes(key)) {
                continue;
            }
            const value = data[key];
            const fullKey = prefix ? `${prefix}_${key}` : key;
            // 新增: 打印 fullKey 和 value 以便调试
            this.logService.info(`renderDropdownItems: fullKey=${fullKey}, value=${JSON.stringify(value)}`);
            // 创建下拉项并设置文本与样式
            const item = doc.createElement('div');
            item.className = 'object-dropdown-item';
            item.textContent = fullKey;
            item.style.paddingLeft = `${level * 16}px`;
            // 鼠标悬停时展示节点详细内容
            try {
                if (value !== null && value !== undefined && typeof value === 'object') {
                    // 非叶子节点预览
                    const keyCount = Object.keys(value).length;
                    const previewValues = Object.entries(value)
                        .slice(0, 3)
                        .map(([k, v]) => `${k}: ${typeof v === 'object' ? '{...}' : String(v)}`)
                        .join(', ');
                    const moreKeysHint = keyCount > 3 ? `, ... (共 ${keyCount} 项)` : '';
                    item.title = `${fullKey} 包含: {${previewValues}${moreKeysHint}}`;
                } else {
                    // 叶子节点显示值
                    item.title = `${fullKey}: ${String(value)}`;
                }
            } catch (e) {
                item.title = `${fullKey}: ${typeof value === 'object' ? '(复杂对象)' : String(value)}`;
            }
            item.addEventListener('click', () => {
                input.value = fullKey;
                this.updateParameterValues(doc);
                dropdown.style.display = 'none';
            });
            dropdown.appendChild(item);
            // 如果当前节点是对象类型，且未超过最大深度，则递归渲染子节点
            if (value && typeof value === 'object' && level + 1 < maxDepth) {
                this.renderDropdownItems(doc, dropdown, value, input, level + 1, fullKey, keyType, maxDepth);
            }
        }
    }

    private renderPositionItems(doc: Document, dropdown: HTMLElement, data: Record<string, any>, input: HTMLInputElement, prefix: string = '', level: number = 0): void {
        for (const key in data) {
            const value = data[key];
            const fullKey = prefix ? `${prefix}_${key}` : key;
            // 如果当前节点具有 position 属性，则添加该节点
            if (value && typeof value === 'object' && 'position' in value) {
                const item = doc.createElement('div');
                item.className = 'object-dropdown-item';
                item.textContent = fullKey;
                item.style.paddingLeft = `${level * 16}px`;
                // 悬停提示显示 position 对象简要
                try {
                    const pos = value.position;
                    item.title = Object.entries(pos)
                        .map(([k, v]) => `${k}: ${v}`)
                        .join(', ');
                } catch (e) {
                    item.title = fullKey;
                }
                item.addEventListener('click', () => {
                    input.value = fullKey;
                    this.updateParameterValues(doc);
                    dropdown.style.display = 'none';
                });
                dropdown.appendChild(item);
            }
            // 递归搜索子节点
            if (value && typeof value === 'object') {
                this.renderPositionItems(doc, dropdown, value, input, fullKey, level + 1);
            }
        }
    }

    /**
     * 渲染所有包含 region 属性的节点
     */
    private renderRegionItems(doc: Document, dropdown: HTMLElement, data: Record<string, any>, input: HTMLInputElement, prefix: string = '', level: number = 0): void {
        for (const key in data) {
            const value = data[key];
            const fullKey = prefix ? `${prefix}_${key}` : key;
            // 如果当前节点具有 region 属性，则显示该节点
            if (value && typeof value === 'object' && 'region' in value) {
                const item = doc.createElement('div');
                item.className = 'object-dropdown-item';
                item.textContent = fullKey;
                item.style.paddingLeft = `${level * 16}px`;
                // 悬停提示 region 对象内容
                try {
                    const reg = value.region;
                    item.title = Object.entries(reg)
                        .map(([k, v]) => `${k}: ${v}`)
                        .join(', ');
                } catch {
                    item.title = fullKey;
                }
                item.addEventListener('click', () => {
                    input.value = fullKey;
                    this.updateParameterValues(doc);
                    dropdown.style.display = 'none';
                });
                dropdown.appendChild(item);
            }
            // 递归渲染子节点
            if (value && typeof value === 'object') {
                this.renderRegionItems(doc, dropdown, value, input, fullKey, level + 1);
            }
        }
    }

    /**
     * 渲染所有包含 icon 属性的节点
     */
    private renderIconItems(doc: Document, dropdown: HTMLElement, data: Record<string, any>, input: HTMLInputElement, prefix: string = '', level: number = 0): void {
        for (const key in data) {
            const value = data[key];
            const fullKey = prefix ? `${prefix}_${key}` : key;
            // 如果当前节点具有 icon 属性，则显示该节点
            if (value && typeof value === 'object' && 'icon' in value) {
                const item = doc.createElement('div');
                item.className = 'object-dropdown-item';
                item.textContent = fullKey;
                item.style.paddingLeft = `${level * 16}px`;
                // 悬停提示 icon 对象内容
                try {
                    const ic = value.icon;
                    item.title = Array.isArray(ic) ? ic.join(', ') : String(ic);
                } catch {
                    item.title = fullKey;
                }
                item.addEventListener('click', () => {
                    input.value = fullKey;
                    this.updateParameterValues(doc);
                    dropdown.style.display = 'none';
                });
                dropdown.appendChild(item);
            }
            // 递归渲染子节点
            if (value && typeof value === 'object') {
                this.renderIconItems(doc, dropdown, value, input, fullKey, level + 1);
            }
        }
    }

    /**
     * 渲染快捷键节点（快捷键下的所有子项）
     */
    private renderHotkeyItems(doc: Document, dropdown: HTMLElement, data: Record<string, any>, input: HTMLInputElement): void {
        // 查找"快捷键"节点
        const hotkeyNode = data['快捷键'];
        if (!hotkeyNode || typeof hotkeyNode !== 'object') {
            // 如果没有快捷键节点，显示提示
            const item = doc.createElement('div');
            item.className = 'object-dropdown-item';
            item.textContent = '暂无快捷键配置';
            item.style.color = '#666';
            item.style.fontStyle = 'italic';
            dropdown.appendChild(item);
            return;
        }

        // 渲染快捷键节点下的所有项
        for (const key in hotkeyNode) {
            const value = hotkeyNode[key];
            if (typeof value === 'string') {
                const item = doc.createElement('div');
                item.className = 'object-dropdown-item';
                item.textContent = key;
                item.title = `快捷键: ${value}`;
                item.addEventListener('click', () => {
                    input.value = key;
                    this.updateParameterValues(doc);
                    dropdown.style.display = 'none';
                });
                dropdown.appendChild(item);
            }
        }
    }

    /**
     * 自行解析app_menu.py文件获取应用列表
     * 当外部未提供应用列表时，窗口可以自主加载app_menu.py
     */
    public async loadAppMenuClasses(): Promise<void> {
        this.logService.info('窗口内部 - 开始加载app_menu.py');

        if (this.appList && this.appList.length > 0) {
            this.logService.info('已有应用列表，长度:', this.appList.length);
            return;
        }

        // 🔥 修复：使用GATPathResolver获取KylinRobot-v2根路径，支持内置模块
        const pathResolverModule = await import('../../common/pathResolver.js');
        const pathResolver = new pathResolverModule.GATPathResolver(
            this.configurationService,
            this.workspaceContextService,
            this.fileService,
            this.logService
        );

        const kylinRobotRootPath = await pathResolver.getKylinRobotRootPath();
        if (!kylinRobotRootPath) {
            this.logService.error('无法获取 KylinRobot-v2 根路径，无法加载 app_menu.py');
            return;
        }

        const appMenuPath = joinPath(kylinRobotRootPath, 'app_menu.py');
        this.logService.info('使用KylinRobot-v2根路径获取 app_menu.py:', appMenuPath.toString());

        try {
            const exists = await this.fileService.exists(appMenuPath);
            if (!exists) {
                this.logService.error('未找到 app_menu.py:', appMenuPath.toString());
                return;
            }
            const content = await this.fileService.readFile(appMenuPath);
            const result = this.parseAppMenu(content.value.toString());
            if (result.length > 0) {
                this.appList = result;
                this.logService.info('窗口内部加载应用列表成功，数量:', result.length);
            } else {
                this.logService.warn('解析 app_menu.py 未获取到任何应用对象');
            }
        } catch (error) {
            this.logService.error('加载 app_menu.py 失败:', error);
        }
    }

    /**
     * 解析app_menu.py文件内容，提取应用类信息
     */
    private parseAppMenu(content: string): Array<{ label: string, value: string, en?: string }> {
        const result: Array<{ label: string, value: string, en?: string }> = [];

        try {
            // 使用正则表达式匹配所有@dataclass(frozen=True)类定义
            const classRegex = /@dataclass\(frozen=True\)[\s\n]*class\s+(\w+):/g;
            let match;

            while ((match = classRegex.exec(content)) !== null) {
                const className = match[1];

                // 获取类的属性范围
                const classStart = match.index;
                const nextClassMatch = content.indexOf('@dataclass(frozen=True)', classStart + 1);
                const classEnd = nextClassMatch !== -1 ? nextClassMatch : content.length;

                const classContent = content.substring(classStart, classEnd);

                // 解析类的zh、en和exec属性
                const zhMatch = /zh:\s*str\s*=\s*'([^']*)'/.exec(classContent);
                const enMatch = /en:\s*str\s*=\s*'([^']*)'/.exec(classContent);
                const execMatch = /exec:\s*str\s*=\s*'([^']*)'/.exec(classContent);

                const label = zhMatch ? zhMatch[1] : className;
                const value = execMatch ? execMatch[1] : className;
                const en = enMatch ? enMatch[1] : '';

                result.push({
                    label: label,
                    value: value,
                    en: en
                });

                this.logService.debug('解析到应用类:', className, 'label=', label, 'value=', value, 'en=', en);
            }

            this.logService.info('总共从app_menu.py解析出应用类数量:', result.length);
        } catch (error) {
            this.logService.error('解析app_menu.py出错:', error);
        }
        return result;
    }

    /**
     * 方法窗口加载后，初始化窗口，构建UI和事件监听器
     */
    public async onWindowLoaded(win: Window): Promise<void> {
        this.methodWindow = win;
        const doc = win.document;

        try {
            this.logService.info('窗口已加载，正在初始化UI...');

            // 获取窗口document
            if (!doc) {
                throw new Error('无法获取窗口document对象');
            }

            // 为新窗口重新设置控制模态窗口确认结果监听器
            try {
                if (win.electronAPI && win.electronAPI.ipcRenderer) {
                    this.logService.info('为新窗口设置控制模态确认结果的IPC监听器');
                    win.electronAPI.ipcRenderer.on('control-modal-result', (_event: any, result: { confirmed: boolean, data: any }) => {
                        this.logService.info(`新窗口通过IPC收到控制模态确认结果: ${JSON.stringify(result)}`);
                        this.dispatcher.handleControlModalResult(result);
                    });
                }

                // 添加window消息监听
                win.addEventListener('message', (event) => {
                    if (event.data && event.data.type === 'control-modal-result') {
                        this.logService.info(`新窗口通过message事件收到控制模态确认结果: ${JSON.stringify(event.data.result)}`);
                        this.dispatcher.handleControlModalResult(event.data.result);
                    }
                });

                this.logService.info('已为新窗口设置控制模态窗口确认结果监听器');
            } catch (error) {
                this.logService.error(`为新窗口设置控制模态窗口确认结果监听器失败: ${error}`);
            }

            // 尝试自行加载应用列表（若外部未提供）
            if (!this.appList || this.appList.length === 0) {
                this.logService.info('外部未提供应用列表，尝试窗口内部加载app_menu.py');
                await this.loadAppMenuClasses();
            }

            // 构建UI和事件监听器
            this.setupEventListeners(
                doc,
                (methodName, parameters) => {
                    // 触发方法插入事件
                    if (this.methodWindow) {
                        this.methodWindow.postMessage({
                            type: 'insert-method',
                            methodName,
                            parameters
                        }, '*');
                    }
                    this.logService.info(`方法插入事件已触发: ${methodName}`, parameters);
                },
                () => {
                    // 关闭窗口
                    if (this.methodWindow) {
                        this.methodWindow.close();
                    }
                    this.logService.info('关闭窗口事件已触发');
                }
            );

            this.logService.info('窗口初始化完成');
        } catch (error) {
            this.logService.error('初始化窗口出错:', error);
        }
    }

    override dispose(): void {
        this.widgetCaptureManager.dispose();

        // 清理所有disposables
        this.disposables.forEach(d => {
            try {
                d.dispose();
            } catch (e) {
                this.logService.error('清理disposable时出错:', e);
            }
        });
        this.disposables.clear();

        super.dispose();
    }
}
