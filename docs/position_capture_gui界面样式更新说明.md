# 坐标采集器界面样式更新说明

## 概述

将 `position_capture_gui.py` 的界面样式更新为与插入公共方法窗口保持一致的设计风格，提升用户体验的一致性。

## 设计原则

### 1. 颜色方案统一
参考插入公共方法窗口的颜色方案：
- **背景色**: `#ffffff` (纯白色)
- **前景色**: `#333333` (深灰色文字)
- **边框色**: `#d4d4d4` (浅灰色边框)
- **主按钮**: `#0e639c` (蓝色背景) / `#ffffff` (白色文字)
- **次按钮**: `#0e639c` (蓝色背景) / `#ffffff` (白色文字)
- **焦点色**: `#007fd4` (蓝色焦点边框)
- **锁定状态**: `#fff3cd` (浅黄色背景) / `#856404` (深黄色文字)

### 2. 字体系统
使用系统字体栈，确保跨平台一致性：
```
-apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>
```

### 3. 布局设计
- **容器内边距**: 16px
- **元素间距**: 8px / 16px
- **分隔线**: 使用 `ttk.Separator` 分隔不同功能区域
- **按钮组**: 右对齐，符合常见的对话框设计

## 主要更新内容

### 1. 样式配置更新

**更新前**:
```python
colors = {
    'bg': '#ffffff',
    'frame_bg': '#f8f9fa',
    'primary': '#2c3e50',
    'accent': '#3498db',
    # ...
}
```

**更新后**:
```python
colors = {
    'background': '#ffffff',
    'foreground': '#333333',
    'border_color': '#d4d4d4',
    'button_background': '#0e639c',
    'button_foreground': '#ffffff',
    'focus_border': '#007fd4',
    # ...
}
```

### 2. 布局结构优化

#### 原始布局
- 使用 `LabelFrame` 创建卡片式布局
- 较大的内边距和间距
- 传统的表单设计

#### 新布局
- 使用简洁的 `Frame` 布局
- 添加分隔线区分功能区域
- 更紧凑的间距设计
- 参考现代Web表单设计

### 3. 组件样式更新

#### 按钮样式
```python
# 主按钮
style.configure('Primary.TButton',
               background=colors['button_primary_background'],
               foreground=colors['button_primary_foreground'],
               font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11),
               padding=(12, 6),
               relief='flat')

# 次按钮
style.configure('Secondary.TButton',
               background=colors['button_background'],
               foreground=colors['button_foreground'],
               font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11),
               padding=(12, 6),
               relief='flat')
```

#### 输入框样式
```python
style.configure('App.TEntry',
               fieldbackground=colors['input_background'],
               foreground=colors['input_foreground'],
               bordercolor=colors['border_color'],
               font=('-apple-system', 'BlinkMacSystemFont', 'Segoe UI', 'Roboto', 11),
               padding=(8, 6))
```

#### 锁定状态样式
```python
style.configure('LockedCoord.TLabel', 
               background=colors['locked_background'], 
               foreground=colors['locked_foreground'], 
               font=('Consolas', 'Monaco', 'monospace', 11, 'bold'),
               relief='solid',
               borderwidth=1)
```

### 4. 功能区域重新组织

#### 标题区域
- 使用更大的字体 (16pt)
- 添加环境信息副标题

#### 基本信息区域
- 移除卡片边框
- 使用标签+输入框的简洁布局

#### 坐标采集区域
- 实时坐标和锁定坐标并排显示
- 添加分隔线分隔不同功能

#### 采集方式选择
- 按钮组样式
- 主次按钮区分

#### 手动输入区域
- 更紧凑的输入框布局
- 按钮右对齐

#### 底部操作区域
- 参考对话框设计
- 取消/确定按钮右对齐

### 5. 窗口尺寸调整

**更新前**: 580x480
**更新后**: 520x420

更紧凑的窗口尺寸，适应新的布局设计。

## 视觉效果对比

### 更新前
- 卡片式设计，较为传统
- 颜色对比度较低
- 布局较为松散

### 更新后
- 现代化的扁平设计
- 清晰的视觉层次
- 紧凑而不拥挤的布局
- 与插入公共方法窗口保持一致的视觉风格

## 用户体验改进

1. **视觉一致性**: 与应用中其他窗口保持统一的设计语言
2. **信息层次**: 通过分隔线和间距清晰区分功能区域
3. **操作便利性**: 按钮布局符合用户习惯
4. **状态反馈**: 锁定状态使用醒目但不刺眼的颜色提示

## 技术实现要点

1. **样式继承**: 充分利用 ttk.Style 的样式系统
2. **响应式布局**: 使用 grid 布局确保界面适应性
3. **跨平台兼容**: 字体和颜色选择考虑不同操作系统
4. **可维护性**: 样式配置集中管理，便于后续调整

## 总结

通过参考插入公共方法窗口的设计，坐标采集器界面实现了：
- 视觉风格的统一性
- 用户体验的一致性
- 现代化的界面设计
- 更好的信息组织和展示

这次更新不仅提升了界面的美观度，也增强了用户在使用不同功能时的连贯体验。
