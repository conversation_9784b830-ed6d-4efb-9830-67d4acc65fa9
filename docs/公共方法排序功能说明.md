# 公共方法排序功能说明

## 功能概述

在应用的左侧"公共方法"栏中，现在支持按照预定义的优先级顺序对方法文件进行排序显示。

## 排序规则

文件按照以下优先级顺序进行排序：

1. **鼠标** - 包含"鼠标"关键词的文件（如：鼠标操作.yml、鼠标点击.yml）
2. **键盘** - 包含"键盘"关键词的文件（如：键盘操作.yml、键盘输入.yml）
3. **窗口** - 包含"窗口"关键词的文件（如：窗口操作.yml、窗口管理.yml）
4. **检查点** - 包含"检查点"关键词的文件（如：检查点验证.yml）
5. **流程** - 包含"流程"关键词的文件（如：流程控制.yml）
6. **电源** - 包含"电源"关键词的文件（如：电源管理.yml）
7. **其他** - 不包含上述关键词的文件，按文件名字母顺序排序

## 实现细节

### 核心方法

#### `sortFilesByPriority(fileEntries)`
- **功能**：对文件条目数组按照预定义优先级进行排序
- **参数**：`fileEntries` - 文件条目数组
- **返回值**：排序后的文件条目数组

#### `extractKeywordFromFileName(fileName)`
- **功能**：从文件名中提取关键词
- **参数**：`fileName` - 文件名（包含扩展名）
- **返回值**：提取的关键词或原文件名（不含扩展名）

### 排序逻辑

1. **关键词提取**：从文件名中提取预定义的关键词
2. **优先级比较**：
   - 如果两个文件都包含优先级关键词，按优先级顺序排序
   - 如果只有一个文件包含优先级关键词，该文件排在前面
   - 如果两个文件都不包含优先级关键词，按文件名字母顺序排序

### 文件位置

- **主要实现文件**：`src/vs/workbench/contrib/gat/browser/views/commonMethodView.ts`
- **修改的方法**：`loadYamlFiles()` - 在加载YAML文件时应用排序
- **新增方法**：
  - `sortFilesByPriority()` - 排序逻辑
  - `extractKeywordFromFileName()` - 关键词提取

## 使用效果

用户在打开公共方法栏时，会看到方法文件按照以下顺序显示：

```
📁 公共方法
├── 鼠标操作.yml
├── 鼠标点击.yml
├── 键盘操作.yml
├── 窗口操作.yml
├── 检查点验证.yml
├── 流程控制.yml
├── 电源管理.yml
└── 其他功能.yml
```

这样的排序使得用户能够更快速地找到常用的操作类型，提高了工作效率。

## 界面优化

### 按钮文字更新

公共方法栏中的切换按钮文字已更新为更清晰的描述：

- **原文字**：
  - "展示方法名" / "展示方法概述"
- **新文字**：
  - "英文名称" / "中文名称"

这样的命名更准确地反映了按钮的功能：在英文方法名和中文方法概述之间切换显示。

## 兼容性

- 该功能向后兼容，不会影响现有的公共方法文件
- 如果文件名不包含预定义关键词，仍会正常显示，只是排在优先级文件之后
- 排序仅影响显示顺序，不会修改文件系统中的实际文件顺序

## 测试验证

功能已通过单元测试验证，确保排序逻辑正确工作。测试覆盖了：

- 基本排序功能
- 关键词提取逻辑
- 边界情况处理（不包含关键词的文件）
- 同类型文件的子排序（如多个鼠标相关文件）
